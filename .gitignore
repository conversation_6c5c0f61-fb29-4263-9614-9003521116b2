# Node.js
node_modules/

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# dotenv environment variables
.env
.env.*

# OS generated files
.DS_Store
Thumbs.db

# Editor directories and files
.vscode/
.idea/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Build output
dist/
build/

# Optional: Ignore coverage reports
coverage/

# Optional: Ignore local development files
*.local 

src/services/file-cool/paddle/test.md
src/services/file-cool/paddle/test.pdf
src/services/file-cool/paddle/img

src/services/file-cool/gongwen/test.docx
src/services/file-cool/gongwen/test.md

src/services/file-cool/excel/test.md
src/services/file-cool/excel/test.xlsx

src/services/file-cool/zhipu/test.md
src/services/file-cool/zhipu/test.png
src/services/file-cool/zhipu/test.jpg