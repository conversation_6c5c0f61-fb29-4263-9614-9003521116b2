{"name": "mcp-gateway", "version": "1.0.0", "description": "Gateway server with CAS SSO and API Key management", "type": "module", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "build:css": "tailwindcss -i ./src/styles/input.css -o ./public/css/styles.css"}, "dependencies": {"@antv/mcp-server-chart": "^0.7.1", "@mendable/firecrawl-js": "^1.21.1", "@modelcontextprotocol/sdk": "^1.12.1", "@playwright/mcp": "^0.0.29", "@tavily/core": "^0.5.6", "axios": "^1.9.0", "chalk": "^4.1.2", "connect-redis": "^8.1.0", "docx": "^9.5.1", "dotenv": "^16.5.0", "ejs": "^3.1.10", "express": "^5.1.0", "express-session": "^1.18.1", "i18next": "^25.2.1", "i18next-express-middleware": "^2.0.0", "i18next-fs-backend": "^2.6.0", "i18next-http-middleware": "^3.7.4", "mongoose": "^8.15.1", "multer": "^2.0.1", "n2c": "^1.1.0", "redis": "^5.5.6", "rotating-file-stream": "^3.2.6", "uuid": "^11.1.0", "xlsx": "^0.18.5", "zod": "^3.25.62"}, "devDependencies": {"autoprefixer": "^10.4.21", "nodemon": "^3.1.10", "postcss": "^8.5.5", "tailwindcss": "^4.1.10"}}