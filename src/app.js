import "dotenv/config";
import express from "express";
import session from "express-session";
import path from "path";
import fs from "fs";
const { readdirSync, lstatSync, existsSync } = fs;
import i18next from "i18next";
import i18nextMiddleware from "i18next-http-middleware";
import Backend from "i18next-fs-backend";
import { RedisStore } from "connect-redis";

// Services and middlewares
import { logAccess } from "./services/logger.js";
import { createRedisClient } from "./services/redis.js";
import { connectMongoDB } from "./services/mongodb.js";
import { requireAuth } from "./middlewares/auth.js";

// 配置常量
const CONFIG = {
  session: {
    key: process.env.SESSION_KEY || "mcp_sess",
    maxAge: parseInt(process.env.SESSION_MAX_AGE || "86400000", 10),
    secret: process.env.SESSION_SECRET,
    cookie: {
      maxAge: parseInt(process.env.SESSION_MAX_AGE || "86400000", 10),
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
    },
  },
  server: {
    port: process.env.PORT || 3000,
  },
  paths: {
    public: path.join(process.cwd(), "public"),
    views: path.join(process.cwd(), "src", "views"),
    locales: path.join(process.cwd(), "src", "locales"),
    mcp: path.join(process.cwd(), "src", "mcp"),
  },
};

// 初始化 i18next
const initI18next = () => {
  const mainLocalesPath = path.join(CONFIG.paths.locales, "zh");
  const mainNamespaces = readdirSync(mainLocalesPath)
    .filter((file) => file.endsWith(".json"))
    .map((file) => file.replace(".json", ""));

  const mcpNamespaces = readdirSync(CONFIG.paths.mcp).filter((dir) => {
    const subdir = path.join(CONFIG.paths.mcp, dir);
    return (
      lstatSync(subdir).isDirectory() &&
      existsSync(path.join(subdir, "locales"))
    );
  });

  const ns = [...mainNamespaces, ...mcpNamespaces];
  const defaultNS = "translation";

  return i18next
    .use(Backend)
    .use(i18nextMiddleware.LanguageDetector)
    .init({
      preload: readdirSync(CONFIG.paths.locales).filter((fileName) =>
        lstatSync(path.join(CONFIG.paths.locales, fileName)).isDirectory()
      ),
      fallbackLng: "zh",
      ns,
      defaultNS,
      backend: {
        loadPath: (lngs, namespaces) => {
          if (!Array.isArray(namespaces)) namespaces = [namespaces];
          if (!Array.isArray(lngs)) lngs = [lngs];

          const lng = lngs[0];
          const ns = namespaces[0];

          if (mainNamespaces.includes(ns)) {
            return path.join(CONFIG.paths.locales, `${lng}/${ns}.json`);
          }

          if (mcpNamespaces.includes(ns)) {
            return path.join(
              CONFIG.paths.mcp,
              `${ns}/locales/${lng}/translation.json`
            );
          }

          return path.join(CONFIG.paths.locales, `${lng}/${defaultNS}.json`);
        },
      },
      detection: {
        order: ["cookie", "querystring", "header"],
        lookupCookie: "i18next",
        caches: ["cookie"],
        cookieExpirationDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year
      },
    });
};

// 加载 MCP 路由
const loadMcpRoutes = async (app) => {
  if (existsSync(CONFIG.paths.mcp)) {
    const dirs = readdirSync(CONFIG.paths.mcp);
    for (const dir of dirs) {
      const routerPath = path.join(CONFIG.paths.mcp, dir, "router.js");
      if (existsSync(routerPath)) {
        const { default: router } = await import(path.resolve(routerPath));
        app.use("/mcp", requireAuth, router);
      }
    }
  }
};

// 创建 Express 应用
const createApp = async (redisClient) => {
  const app = express();

  // 基础中间件
  app.use(express.json());
  app.use(express.urlencoded({ extended: true }));
  app.use(express.static(CONFIG.paths.public));

  // i18n 中间件
  app.use(i18nextMiddleware.handle(i18next));

  // 视图引擎设置
  app.set("view engine", "ejs");
  app.set("views", CONFIG.paths.views);

  // 添加 i18next 到所有视图
  app.use((req, res, next) => {
    res.locals.t = req.i18n.t;
    res.locals.i18next = req.i18n;
    next();
  });

  // Session 中间件
  app.use(
    session({
      store: new RedisStore({
        client: redisClient,
        prefix: "sess:",
        ttl: CONFIG.session.maxAge / 1000, // 转换为秒
      }),
      secret: CONFIG.session.secret,
      name: CONFIG.session.key,
      cookie: CONFIG.session.cookie,
      resave: false,
      saveUninitialized: false,
    })
  );

  // 访问日志中间件
  app.use(logAccess);

  // 路由
  const { default: indexRouter } = await import("./routes/index.js");
  const { default: mcpserverRouter } = await import("./routes/mcpserver.js");
  const { default: proxyRouter } = await import("./routes/proxy.js");

  app.use("/", indexRouter);
  app.use("/mcp", mcpserverRouter);
  app.use("/proxy", proxyRouter);

  // 加载 MCP 路由
  await loadMcpRoutes(app);

  // 错误处理中间件
  app.use((err, req, res, next) => {
    console.error("Server error:", err);
    res.status(err.status || 500).json({
      error: {
        message:
          process.env.NODE_ENV === "production"
            ? "Internal Server Error"
            : err.message,
        status: err.status || 500,
      },
    });
  });

  return app;
};

// 优雅退出处理
const setupGracefulShutdown = () => {
  const handleShutdown = (signal) => {
    console.log(`${signal} signal received: closing HTTP server`);
    process.exit(0);
  };

  process.on("SIGTERM", () => handleShutdown("SIGTERM"));
  process.on("SIGINT", () => handleShutdown("SIGINT"));
};

// 启动服务器
const startServer = async () => {
  try {
    // 初始化 i18next
    await initI18next();

    // 初始化 Redis
    const redisClient = await createRedisClient();

    // 初始化 MongoDB
    await connectMongoDB();

    // 创建并启动应用
    const app = await createApp(redisClient);
    app.listen(CONFIG.server.port, () => {
      console.log(`Server running on port ${CONFIG.server.port}`);
      console.log(`Environment: ${process.env.NODE_ENV || "development"}`);
    });

    // 设置优雅退出
    setupGracefulShutdown();
  } catch (error) {
    console.error("Failed to start server:", error);
    process.exit(1);
  }
};

// 启动应用
startServer();
