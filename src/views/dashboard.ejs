<!DOCTYPE html>
<html lang="<%= i18next.language %>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= t('title') %> - <%= t('dashboard.apiKey.title') %></title>
    <link href="/css/styles.css" rel="stylesheet">
</head>
<body class="bg-gray-100">
    <div class="min-h-screen">
        <nav class="bg-white shadow-sm">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between h-16">
                    <div class="flex items-center">
                        <h1 class="text-xl font-bold text-gray-900"><%= t('title') %></h1>
                    </div>
                    <div class="flex items-center">
                        <span class="text-gray-700 mr-4"><%= t('dashboard.welcome', { username: user.username }) %></span>
                        <select id="languageSelect" class="mr-4 px-2 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500">
                            <option value="en" <%= i18next.resolvedLanguage === 'en' ? 'selected' : '' %>>🇺🇸 English</option>
                            <option value="zh" <%= i18next.resolvedLanguage === 'zh' ? 'selected' : '' %>>🇨🇳 中文</option>
                        </select>
                        <a href="/logout" class="text-indigo-600 hover:text-indigo-900"><%= t('dashboard.logout') %></a>
                    </div>
                </div>
            </div>
        </nav>

        <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
            <div class="px-4 py-6 sm:px-0">
                <div class="bg-white shadow rounded-lg p-6">
                    <h2 class="text-lg font-medium text-gray-900 mb-4"><%= t('dashboard.apiKey.title') %></h2>
                    
                    <div id="apiKeySection" class="space-y-4">
                        <% if (apiKey) { %>
                            <div class="bg-gray-50 p-4 rounded-md">
                                <p class="text-sm text-gray-600 mb-2"><%= t('dashboard.apiKey.yourKey') %></p>
                                <div class="flex items-center space-x-2">
                                    <code class="bg-gray-100 px-2 py-1 rounded text-sm font-mono"><%= apiKey %></code>
                                    <button onclick="copyApiKey()" class="text-indigo-600 hover:text-indigo-900 text-sm">
                                        <%= t('dashboard.apiKey.copy') %>
                                    </button>
                                </div>
                                <p class="text-xs text-gray-500 mt-2"><%= t('dashboard.apiKey.expire') %></p>
                            </div>
                            <button onclick="deleteApiKey()" 
                                    class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                                <%= t('dashboard.apiKey.delete') %>
                            </button>
                        <% } else { %>
                            <button onclick="generateApiKey()" 
                                    class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                <%= t('dashboard.apiKey.generate') %>
                            </button>
                        <% } %>
                    </div>
                </div>
                
                <% if (partialsList && partialsList.length) { %>
                  <hr class="mt-8 border-t-2" style="border-color: #6b7280 !important;">
                  <h3 class="mt-6 mb-6 ml-0 text-lg font-medium text-gray-900">MCP工具配置</h3>
                  <% partialsList.forEach(function(partial) { %>
                    <%- include(partial) %>
                  <% }) %>
                <% } %>
            </div>
        </main>
    </div>

    <script>
        // Language selection handler
        document.getElementById('languageSelect').addEventListener('change', function(e) {
            const language = e.target.value;
            document.cookie = `i18next=${language};path=/;max-age=31536000`; // 1 year expiration
            window.location.reload();
        });

        async function generateApiKey() {
            try {
                const response = await fetch('/api/generate-key', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                if (response.ok) {
                    window.location.reload();
                }
            } catch (error) {
                console.error('Error generating API key:', error);
            }
        }

        async function deleteApiKey() {
            if (!confirm('<%= t("dashboard.apiKey.deleteConfirm") %>')) return;
            
            try {
                const response = await fetch('/api/delete-key', {
                    method: 'DELETE'
                });
                if (response.ok) {
                    window.location.reload();
                }
            } catch (error) {
                console.error('Error deleting API key:', error);
            }
        }

        function copyApiKey() {
            const apiKey = document.querySelector('code').textContent;
            navigator.clipboard.writeText(apiKey).then(() => {
                alert('<%= t("dashboard.apiKey.copySuccess") %>');
            });
        }
    </script>
</body>
</html> 