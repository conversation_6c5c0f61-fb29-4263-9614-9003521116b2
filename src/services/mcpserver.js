import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import fs from "fs";
import path from "path";

/**
 * Get tools for a specific MCP directory
 * @param {string} dirName - Directory name
 * @returns {Array} - Array of tools for the specified directory
 */
async function getToolsByDirectory(dirName) {
  const mcpDir = path.join(process.cwd(), "src", "mcp");
  const toolsPath = path.join(mcpDir, dirName, "tools.js");
  
  if (!fs.existsSync(toolsPath)) {
    return [];
  }

  const mod = await import(toolsPath);
  let tools = [];
  
  for (const key of Object.keys(mod)) {
    if (Array.isArray(mod[key])) {
      tools = tools.concat(mod[key]);
    }
  }
  
  return tools;
}

/**
 * Get all available MCP directories
 * @returns {Array} - Array of directory names
 */
function getAvailableMCPDirectories() {
  const mcpDir = path.join(process.cwd(), "src", "mcp");
  return fs
    .readdirSync(mcpDir, { withFileTypes: true })
    .filter((dirent) => dirent.isDirectory())
    .map((dirent) => dirent.name)
    .filter(dirName => {
      const toolsPath = path.join(mcpDir, dirName, "tools.js");
      return fs.existsSync(toolsPath);
    });
}

/**
 * Create a server for a specific MCP directory
 * @param {string} userId - User ID
 * @param {string} dirName - Directory name
 * @returns {McpServer} - Configured MCP server instance for the directory
 */
async function createServerForDirectory(userId, dirName) {
  const server = new McpServer({
    name: `MCP-${dirName.charAt(0).toUpperCase() + dirName.slice(1)}`,
    version: "0.1.0",
  });

  // 注册指定目录的tools
  const tools = await getToolsByDirectory(dirName);
  for (const tool of tools) {
    server.tool(tool.name, tool.description, tool.inputSchema, async (args) =>
      tool.handler(userId, args)
    );
  }

  return server;
}

export { createServerForDirectory, getAvailableMCPDirectories };
