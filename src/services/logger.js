import fs from "fs";
import path from "path";
import { createStream } from "rotating-file-stream";

// Ensure log directory exists
const logDir = path.join(process.cwd(), "logs");
if (!fs.existsSync(logDir)) {
  fs.mkdirSync(logDir);
}

// Create rotating write stream for access log
const accessLogStream = createStream("access.log", {
  interval: "1d", // rotate daily
  path: logDir,
  size: "10M", // rotate when size exceeds 10MB
  compress: "gzip", // compress rotated files
  maxFiles: 14, // keep logs for 14 days
});

const logAccess = (req, res, next) => {
  const timestamp = new Date().toISOString();
  const { method } = req;
  const originalUrl = req.originalUrl || req.url;
  const ip = req.ip;
  const userAgent = req.headers["user-agent"] || "unknown";
  const userId = req.userId || req.session.user?.id || "anonymous";

  const logEntry =
    JSON.stringify({
      timestamp,
      method,
      url: originalUrl,
      ip,
      userAgent,
      userId,
    }) + "\n";

  accessLogStream.write(logEntry);
  next();
};

export { logAccess };
