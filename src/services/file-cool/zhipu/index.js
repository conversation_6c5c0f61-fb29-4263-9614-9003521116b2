import axios from "axios";
import fs from "fs";

// 获取智谱API Key
function getZhipuApiKey() {
  const apiKey = "11dcd4229aea4fbfbd74f35a84ab3b55.4oHlvwPZxdCfYQ1x";
  if (!apiKey || apiKey === "YOUR_API_KEY") {
    throw new Error("ZHIPU_API_KEY environment variable is not set");
  }
  return apiKey;
}

/**
 * 获取文件的MIME类型
 * @param {string} extension - 文件扩展名
 * @returns {string} MIME类型
 */
function getMimeType(extension) {
  const mimeTypes = {
    jpg: "image/jpeg",
    jpeg: "image/jpeg",
    png: "image/png",
  };

  return mimeTypes[extension.toLowerCase()] || "image/jpeg";
}

/**
 * 使用智谱AI GLM-4.1V-Thinking-Flash模型处理图片文件
 * @param {Buffer} fileBuffer - 图片文件的Buffer
 * @param {string} filename - 原文件名
 * @param {string} customPrompt - 自定义处理指令，默认为markdown格式输出
 * @returns {Promise<Array>} 返回处理结果数组
 */
async function processFileWithZhipu(fileBuffer, filename, customPrompt) {
  const fileExtension = filename.split(".").pop().toLowerCase();
  const supportedImageTypes = ["jpg", "jpeg", "png"];

  if (!supportedImageTypes.includes(fileExtension)) {
    throw new Error(
      `Unsupported file type: ${fileExtension}. Supported types: ${supportedImageTypes.join(
        ", "
      )}`
    );
  }

  if (!customPrompt) {
    customPrompt =
      "请仔细分析这张图片的内容，并以markdown格式输出。如果是文档类图片，请提取其中的文字内容并保持原有格式。如果是图表或流程图，请描述其结构和内容。";
  }

  let data = [];

  try {
    // 将文件buffer转换为base64
    const base64Image = fileBuffer.toString("base64");

    // 构建data URL
    const mimeType = getMimeType(fileExtension);
    const dataUrl = `data:${mimeType};base64,${base64Image}`;

    const apiKey = getZhipuApiKey();
    const url = "https://open.bigmodel.cn/api/paas/v4/chat/completions";
    const headers = {
      "Content-Type": "application/json",
      Authorization: `Bearer ${apiKey}`,
    };

    const body = {
      messages: [
        {
          role: "user",
          content: [
            {
              type: "image_url",
              image_url: { url: dataUrl },
            },
            {
              type: "text",
              text: customPrompt,
            },
          ],
        },
      ],
      model: "glm-4.1v-thinking-flash",
    };

    const response = await axios.post(url, body, { headers });
    const responseData = response.data;

    if (!responseData.choices || !responseData.choices.length) {
      throw new Error("No choices returned from Zhipu API");
    }

    // 提取响应内容
    const content = responseData.choices[0].message.content || "";
    // const reasoningContent = responseData.choices[0].message.reasoning_content || "";

    // 组合最终内容
    let finalContent = "";
    // if (reasoningContent) {
    //   finalContent += `<Think>\n${reasoningContent}\n</Think>\n\n`;
    // }
    finalContent += content;

    // 生成markdown文件名
    const mdFilename = filename.replace(/\.[^/.]+$/, "") + ".md";
    const mdBase64 = Buffer.from(finalContent, "utf-8").toString("base64");

    data.push({
      filename: mdFilename,
      blob: mdBase64,
    });
  } catch (error) {
    console.error("Error during Zhipu API request:", error.message || error);
    throw new Error(
      `Zhipu API processing failed: ${
        error.response?.data?.msg || error.message
      }`
    );
  }

  return data;
}

/**
 * 测试函数
 */
async function main() {
  try {
    // 测试图片处理
    const testImagePath = "./test.png"; // 需要准备一个测试图片
    if (fs.existsSync(testImagePath)) {
      const file = fs.readFileSync(testImagePath);
      const data = await processFileWithZhipu(file, "test.jpg");
      fs.writeFileSync("./test.md", Buffer.from(data[0].blob, "base64"));
      console.log("处理完成，结果已保存到 test.md");
    } else {
      console.log("测试图片不存在");
    }
  } catch (error) {
    console.error("测试失败:", error.message);
  }
}

export { processFileWithZhipu };

// 如果直接运行此文件，执行测试
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}
