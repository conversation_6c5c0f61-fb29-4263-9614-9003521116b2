import {
  Document,
  Packer,
  Paragraph,
  TextRun,
  AlignmentType,
  convertInchesToTwip,
} from "docx";
import n2c from "n2c";
import fs from "fs";

/**
 * 中文数字转换工具
 */
class ChineseNumberConverter {
  static toChineseNumber(num) {
    return n2c(num);
  }
}

/**
 * Markdown解析器
 */
class MarkdownParser {
  constructor() {
    this.headingCounters = {
      h2: 0,
      h3: 0,
      h4: 0,
      h5: 0,
    };
  }

  /**
   * 解析Markdown内容
   */
  parse(markdown) {
    const lines = markdown.split("\n");
    const elements = [];
    let currentListItems = [];
    let currentListType = null;
    let listCounter = 0;
    let afterH1 = false; // 标记是否在一级标题后
    let firstParagraphAfterH1 = false; // 标记是否是一级标题后的第一个段落
    let attachmentStarted = false; // 标记附件是否开始
    let attachmentLines = []; // 存储附件行

    // 预处理：识别落款部分
    const signatureInfo = this.identifySignature(lines);

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();

      if (line === "") {
        // 空行，如果正在处理列表，则结束列表
        if (currentListItems.length > 0) {
          elements.push({
            type: "list",
            listType: currentListType,
            items: currentListItems,
            counter: listCounter,
          });
          currentListItems = [];
          currentListType = null;
        }
        continue;
      }

      // 解析标题
      const headingMatch = line.match(/^(#{1,6})\s+(.+)/);
      if (headingMatch) {
        // 如果正在处理列表，先结束列表
        if (currentListItems.length > 0) {
          elements.push({
            type: "list",
            listType: currentListType,
            items: currentListItems,
            counter: listCounter,
          });
          currentListItems = [];
          currentListType = null;
        }

        const level = headingMatch[1].length;
        const text = headingMatch[2];

        // 设置一级标题后的标志
        if (level === 1) {
          afterH1 = true;
          firstParagraphAfterH1 = true;
          // 一级标题后增加空行
          elements.push({
            type: "heading",
            level: level,
            text: text,
            numbering: this.getHeadingNumbering(level),
          });
          elements.push({
            type: "empty-line",
          });
        } else {
          afterH1 = false;
          firstParagraphAfterH1 = false;
          elements.push({
            type: "heading",
            level: level,
            text: text,
            numbering: this.getHeadingNumbering(level),
          });
        }
        continue;
      }

      // 解析无序列表
      const unorderedListMatch = line.match(/^[-*+]\s+(.+)/);
      if (unorderedListMatch) {
        // 重置一级标题后的标志
        if (firstParagraphAfterH1) {
          firstParagraphAfterH1 = false;
        }
        
        if (currentListType !== "unordered") {
          if (currentListItems.length > 0) {
            elements.push({
              type: "list",
              listType: currentListType,
              items: currentListItems,
              counter: listCounter,
            });
          }
          currentListItems = [];
          currentListType = "unordered";
        }
        currentListItems.push(unorderedListMatch[1]);
        continue;
      }

      // 解析有序列表
      const orderedListMatch = line.match(/^\d+\.\s+(.+)/);
      if (orderedListMatch) {
        // 重置一级标题后的标志
        if (firstParagraphAfterH1) {
          firstParagraphAfterH1 = false;
        }
        
        if (currentListType !== "ordered") {
          if (currentListItems.length > 0) {
            elements.push({
              type: "list",
              listType: currentListType,
              items: currentListItems,
              counter: listCounter,
            });
          }
          currentListItems = [];
          currentListType = "ordered";
          listCounter = 0;
        }
        listCounter++;
        currentListItems.push(orderedListMatch[1]);
        continue;
      }

      // 如果正在处理列表，结束列表
      if (currentListItems.length > 0) {
        elements.push({
          type: "list",
          listType: currentListType,
          items: currentListItems,
          counter: listCounter,
        });
        currentListItems = [];
        currentListType = null;
      }

      // 普通段落
      if (line.length > 0) {
        // 检查是否是附件开始行
        if (line.startsWith("附件：") || line.startsWith("附件:")) {
          // 如果之前有附件行，先处理它们
          if (attachmentLines.length > 0) {
            this.processAttachments(elements, attachmentLines);
            attachmentLines = [];
          }
          
          // 附件前增加空行
          elements.push({
            type: "empty-line",
          });
          
          attachmentStarted = true;
          attachmentLines.push(line);
        } else if (attachmentStarted && line.match(/^\d+\./)) {
          // 附件列表项
          attachmentLines.push(line);
        } else {
          // 如果之前有附件行，先处理它们
          if (attachmentLines.length > 0) {
            this.processAttachments(elements, attachmentLines);
            attachmentLines = [];
            attachmentStarted = false;
            
            // 附件列表后增加空行
            elements.push({
              type: "empty-line",
            });
          }
          
          const isFirstAfterH1 = afterH1 && firstParagraphAfterH1;
          const signatureIndex = signatureInfo.lineIndexes.indexOf(i);
          const isSignature = signatureIndex !== -1;
          
          elements.push({
            type: "paragraph",
            text: line,
            noIndent: isFirstAfterH1, // 一级标题后第一个段落不缩进
            isSignature: isSignature, // 标记是否为落款
            signatureIndent: isSignature ? signatureInfo.indents[signatureIndex] : 0, // 落款缩进值
          });
          
          // 重置标志
          if (firstParagraphAfterH1) {
            firstParagraphAfterH1 = false;
          }
        }
      }
    }

    // 处理最后的列表
    if (currentListItems.length > 0) {
      elements.push({
        type: "list",
        listType: currentListType,
        items: currentListItems,
        counter: listCounter,
      });
    }

    // 处理最后的附件行
    if (attachmentLines.length > 0) {
      this.processAttachments(elements, attachmentLines);
      // 附件列表后增加空行
      elements.push({
        type: "empty-line",
      });
    }

    return elements;
  }

  /**
   * 计算字符串的显示长度
   */
  calculateStringLength(text) {
    let length = 0;
    const chars = Array.from(text);
    
    for (let i = 0; i < chars.length; i++) {
      const char = chars[i];
      const code = char.charCodeAt(0);
      
      // 判断字符类型
      if (this.isChinese(char)) {
        // 中文字符长度为2
        length += 2;
      } else if (this.isFullWidthSymbol(char)) {
        // 全角符号长度为2
        length += 2;
      } else if (this.isSpace(char)) {
        // 空格字符长度为1
        length += 1;
      } else if (this.isEnglishOrNumber(char)) {
        // 英文和数字长度为1
        length += 1;
        // 如果数字和英文字符后是中文字符，需要增加1字符
        if (i + 1 < chars.length && this.isChinese(chars[i + 1])) {
          length += 1;
        }
      } else {
        // 半角符号长度为1
        length += 1;
      }
    }
    
    return length;
  }

  /**
   * 判断是否为中文字符
   */
  isChinese(char) {
    const code = char.charCodeAt(0);
    return (code >= 0x4e00 && code <= 0x9fff) || 
           (code >= 0x3400 && code <= 0x4dbf) || 
           (code >= 0x20000 && code <= 0x2a6df);
  }

  /**
   * 判断是否为全角符号
   */
  isFullWidthSymbol(char) {
    const code = char.charCodeAt(0);
    return (code >= 0xff01 && code <= 0xff5e) || // 全角ASCII符号
           (code >= 0x3000 && code <= 0x303f) || // CJK符号和标点（包含全角空格0x3000）
           (code >= 0x2000 && code <= 0x206f);   // 通用标点
  }

  /**
   * 判断是否为半角空格字符
   */
  isSpace(char) {
    const code = char.charCodeAt(0);
    return code === 0x0020 || // 普通空格
           code === 0x00A0;   // 不间断空格
  }

  /**
   * 判断是否为英文或数字
   */
  isEnglishOrNumber(char) {
    const code = char.charCodeAt(0);
    return (code >= 0x0030 && code <= 0x0039) || // 数字0-9
           (code >= 0x0041 && code <= 0x005a) || // 大写字母A-Z
           (code >= 0x0061 && code <= 0x007a);   // 小写字母a-z
  }

  /**
   * 识别落款部分
   */
  identifySignature(lines) {
    const signatureInfo = {
      lineIndexes: [],
      lines: [],
      maxLength: 0,
      indents: []
    };
    
    const nonEmptyLines = [];
    
    // 收集所有非空行的索引
    for (let i = 0; i < lines.length; i++) {
      if (lines[i].trim() !== "") {
        nonEmptyLines.push(i);
      }
    }
    
    // 从最后开始查找，通常落款在最后2-3行
    const lastNonEmptyLines = nonEmptyLines.slice(-3);
    
    for (const lineIndex of lastNonEmptyLines) {
      const line = lines[lineIndex].trim();
      
      // 识别日期格式：YYYY年MM月DD日
      const datePattern = /^\d{4}年\d{1,2}月\d{1,2}日$/;
      
      // 识别单位名称（通常包含常见的机构关键词）
      const unitKeywords = ['中心', '公司', '部门', '办公室', '委员会', '管理局', '局', '厅', '处', '司', '科', '组', '队', '会', '社', '院', '所', '室', '站', '点', '部', '股', '团', '委', '会', '联合会', '协会', '学会', '研究院', '事业部', '分公司', '子公司', '集团', '控股', '投资', '管理', '发展', '服务'];
      
      // 检查是否包含单位关键词
      const containsUnitKeyword = unitKeywords.some(keyword => line.includes(keyword));
      
      if (datePattern.test(line) || containsUnitKeyword) {
        signatureInfo.lineIndexes.push(lineIndex);
        signatureInfo.lines.push(line);
        
        // 计算每行的字符长度
        const lineLength = this.calculateStringLength(line);
        if (lineLength > signatureInfo.maxLength) {
          signatureInfo.maxLength = lineLength;
        }
      }
    }
    
    // 计算每行的右缩进
    for (const line of signatureInfo.lines) {
      const lineLength = this.calculateStringLength(line);
      const indent = (signatureInfo.maxLength - lineLength) / 2;
      signatureInfo.indents.push(indent);
    }
    
    return signatureInfo;
  }

  /**
   * 处理附件行
   */
  processAttachments(elements, attachmentLines) {
    for (let i = 0; i < attachmentLines.length; i++) {
      const line = attachmentLines[i];
      
      if (line.startsWith("附件：") || line.startsWith("附件:")) {
        // 附件标题行
        elements.push({
          type: "paragraph",
          text: line,
        });
      } else if (line.match(/^\d+\./)) {
        // 附件列表项，需要格式化对齐
        const match = line.match(/^(\d+)\.(.+)/);
        if (match) {
          const number = match[1];
          const content = match[2];
          // 使用制表符或空格来对齐
          const formattedLine = number.padStart(7, " ") + "." + content;
          elements.push({
            type: "paragraph",
            text: formattedLine,
            isAttachment: true,
          });
        }
      }
    }
  }

  /**
   * 获取标题编号
   */
  getHeadingNumbering(level) {
    switch (level) {
      case 1:
        return ""; // 一级标题不需要编号
      case 2:
        this.headingCounters.h2++;
        this.headingCounters.h3 = 0;
        this.headingCounters.h4 = 0;
        this.headingCounters.h5 = 0;
        return (
          ChineseNumberConverter.toChineseNumber(this.headingCounters.h2) + "、"
        );
      case 3:
        this.headingCounters.h3++;
        this.headingCounters.h4 = 0;
        this.headingCounters.h5 = 0;
        return (
          "（" +
          ChineseNumberConverter.toChineseNumber(this.headingCounters.h3) +
          "）"
        );
      case 4:
        this.headingCounters.h4++;
        this.headingCounters.h5 = 0;
        return this.headingCounters.h4 + ".";
      case 5:
        this.headingCounters.h5++;
        return "（" + this.headingCounters.h5 + "）";
      default:
        return "";
    }
  }
}

/**
 * Word文档生成器
 */
class WordDocumentGenerator {
  constructor() {
    this.paragraphSpacing = {
      line: 560, // 28磅 * 20
      lineRule: "exact",
      before: 0,
      after: 0,
    };
    this.indentSize = 480; // 2中文字符的缩进 = 24磅 * 20 = 480 twip
  }

  /**
   * 生成Word文档
   */
  generate(elements) {
    const children = [];

    for (const element of elements) {
      switch (element.type) {
        case "heading":
          children.push(this.createHeading(element));
          break;
        case "paragraph":
          children.push(this.createParagraph(element.text, element.noIndent, element.isAttachment, element.isSignature, element.signatureIndent));
          break;
        case "list":
          children.push(...this.createList(element));
          break;
        case "empty-line":
          children.push(this.createEmptyLine());
          break;
      }
    }

    return new Document({
      sections: [
        {
          properties: {},
          children: children,
        },
      ],
    });
  }

  /**
   * 创建标题
   */
  createHeading(element) {
    const { level, text, numbering } = element;
    const displayText = numbering + text;

    switch (level) {
      case 1:
        return new Paragraph({
          children: [
            new TextRun({
              text: displayText,
              font: "方正小标宋简体",
              size: 44, // 二号字 = 22pt = 44 half-points
              bold: true,
            }),
          ],
          alignment: AlignmentType.CENTER,
          spacing: this.paragraphSpacing,
        });

      case 2:
        return new Paragraph({
          children: [
            new TextRun({
              text: displayText,
              font: "黑体",
              size: 32, // 三号字 = 16pt = 32 half-points
              bold: false,
            }),
          ],
          alignment: AlignmentType.LEFT,
          indent: { firstLine: this.indentSize },
          spacing: this.paragraphSpacing,
        });

      case 3:
        return new Paragraph({
          children: [
            new TextRun({
              text: displayText,
              font: "楷体_GB2312",
              size: 32, // 三号字
              bold: true,
            }),
          ],
          alignment: AlignmentType.LEFT,
          indent: { firstLine: this.indentSize },
          spacing: this.paragraphSpacing,
        });

      case 4:
        return new Paragraph({
          children: [
            new TextRun({
              text: displayText,
              font: "仿宋_GB2312",
              size: 32, // 三号字
              bold: false,
            }),
          ],
          alignment: AlignmentType.LEFT,
          indent: { firstLine: this.indentSize },
          spacing: this.paragraphSpacing,
        });

      case 5:
        return new Paragraph({
          children: [
            new TextRun({
              text: displayText,
              font: "仿宋_GB2312",
              size: 32, // 三号字
              bold: false,
            }),
          ],
          alignment: AlignmentType.LEFT,
          indent: { firstLine: this.indentSize },
          spacing: this.paragraphSpacing,
        });

      default:
        return this.createParagraph(displayText);
    }
  }

  /**
   * 创建段落
   */
  createParagraph(text, noIndent = false, isAttachment = false, isSignature = false, signatureIndent = 0) {
    const paragraph = {
      children: [
        new TextRun({
          text: text,
          font: "仿宋_GB2312",
          size: 32, // 三号字
        }),
      ],
      alignment: AlignmentType.LEFT,
      spacing: this.paragraphSpacing,
    };

    // 落款右对齐并设置右缩进
    if (isSignature) {
      paragraph.alignment = AlignmentType.RIGHT;
      // 将字符缩进转换为twip单位 (1字符约等于12磅，1磅=20twip)
      const rightIndentTwip = signatureIndent * 160;
      paragraph.indent = { right: rightIndentTwip };
    }
    // 如果不是noIndent，则添加首行缩进
    else if (!noIndent) {
      paragraph.indent = { firstLine: this.indentSize };
    }

    // 附件行使用特殊的左缩进来对齐数字
    if (isAttachment) {
      paragraph.indent = { left: this.indentSize };
    }

    return new Paragraph(paragraph);
  }

  /**
   * 创建空行
   */
  createEmptyLine() {
    return new Paragraph({
      children: [
        new TextRun({
          text: "",
          size: 32,
        }),
      ],
      spacing: this.paragraphSpacing,
    });
  }

  /**
   * 创建列表
   */
  createList(element) {
    const { listType, items } = element;
    const paragraphs = [];

    for (let i = 0; i < items.length; i++) {
      const item = items[i];
      let prefix = "";

      if (listType === "unordered") {
        prefix = "• ";
      } else if (listType === "ordered") {
        prefix = i + 1 + ". ";
      }

      paragraphs.push(
        new Paragraph({
          children: [
            new TextRun({
              text: prefix + item,
              font: "仿宋_GB2312",
              size: 32, // 三号字
            }),
          ],
          alignment: AlignmentType.LEFT,
          indent: { firstLine: this.indentSize },
          spacing: this.paragraphSpacing,
        })
      );
    }

    return paragraphs;
  }
}

/**
 * 将Markdown转换为Word文档
 * @param {Buffer} fileBuffer - Markdown文件的Buffer
 * @param {string} filename - 原文件名
 * @returns {Promise<{filename: string, buffer: string}>} 返回转换后的文件名和base64编码的Buffer
 */
export async function processMdToDocx(fileBuffer, filename) {
  try {
    // 解析Markdown内容
    const markdownContent = fileBuffer.toString("utf-8");
    const parser = new MarkdownParser();
    const elements = parser.parse(markdownContent);

    // 生成Word文档
    const generator = new WordDocumentGenerator();
    const doc = generator.generate(elements);

    // 生成文档buffer
    const buffer = await Packer.toBuffer(doc);

    // 生成新的文件名
    const newFilename = filename.replace(/\.md$/i, ".docx");

    // 返回文件名和base64编码的buffer
    return [
      {
        filename: newFilename,
        blob: buffer.toString("base64"),
      },
    ];
  } catch (error) {
    throw new Error(`转换失败: ${error.message}`);
  }
}

// main 测试，支持命令行参数
async function main() {
  const file = fs.readFileSync("./test.md");
  const data = await processMdToDocx(file, "test.md");
  fs.writeFileSync("./test.docx", Buffer.from(data[0].blob, "base64"));
  //   console.log(data);
}

if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}