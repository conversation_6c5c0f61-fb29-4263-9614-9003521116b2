import axios from "axios";
import fs from "fs";

const API_URL = process.env.PADDLE_API_URL;

async function layoutParsing(fileBuffer, filename, fileType) {
  const payload = {
    file: Buffer.from(fileBuffer).toString("base64"),
    fileType: fileType,
  };

  let data = [];

  try {
    const response = await axios.post(API_URL, payload);
    const results = response.data.result.layoutParsingResults;

    const md = results.map((res) => res.markdown.text).join("\n");
    const images = results
      .map((res) => res.markdown.images)
      .filter((images) => Object.keys(images).length > 0);

    const mdBase64 = Buffer.from(md, "utf-8").toString("base64");
    data.push({
      filename: filename.replace(/\.[^/.]+$/, "") + ".md",
      blob: mdBase64,
    });

    if (images.length > 0) {
      for (const imageGroup of images) {
        for (const [key, value] of Object.entries(imageGroup)) {
          data.push({
            filename: key,
            blob: value,
          });
        }
      }
    }
  } catch (error) {
    console.error("Error during API request:", error.message || error);
  }
  return data;
}

async function main() {
  const file = fs.readFileSync("./test.pdf");
  const data = await layoutParsing(file, "test.pdf", 0);
  fs.writeFileSync("./test.md", Buffer.from(data[0].blob, "base64"));
  console.log(data);
}

export { layoutParsing };

if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}
