import XLSX from "xlsx";
import fs from "fs";

/**
 * 将Excel文件转换为Markdown文件
 * @param {Buffer} fileBuffer - Excel文件的buffer
 * @param {string} filename - 原文件名
 * @returns {Object} - 包含filename和blob的对象
 */
export function excelToMarkdown(fileBuffer, filename) {
  try {
    // 读取Excel文件
    const workbook = XLSX.read(fileBuffer, { type: "buffer" });

    let markdownContent = "";

    // 遍历所有工作表
    workbook.SheetNames.forEach((sheetName, index) => {
      const worksheet = workbook.Sheets[sheetName];

      // 添加工作表标题
      if (workbook.SheetNames.length > 1) {
        markdownContent += `## ${sheetName}\n\n`;
      }

      // 将工作表转换为JSON数组
      const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

      if (jsonData.length === 0) {
        markdownContent += "(空表格)\n\n";
        return;
      }

      // 过滤空行
      const filteredData = jsonData.filter((row) =>
        row.some((cell) => cell !== undefined && cell !== null && cell !== "")
      );

      if (filteredData.length === 0) {
        markdownContent += "(空表格)\n\n";
        return;
      }

      // 获取最大列数
      const maxCols = Math.max(...filteredData.map((row) => row.length));

      // 补全每行的列数
      const normalizedData = filteredData.map((row) => {
        const normalizedRow = [...row];
        while (normalizedRow.length < maxCols) {
          normalizedRow.push("");
        }
        return normalizedRow;
      });

              // 生成Markdown表格
        if (normalizedData.length > 0) {
          // 表头
          const headers = normalizedData[0].map((cell) =>
            formatCellValue(cell)
          );
          markdownContent += `| ${headers.join(" | ")} |\n`;

          // 分隔线
          markdownContent += `| ${headers.map(() => "---").join(" | ")} |\n`;

          // 数据行
          for (let i = 1; i < normalizedData.length; i++) {
            const row = normalizedData[i].map((cell) =>
              formatCellValue(cell)
            );
            markdownContent += `| ${row.join(" | ")} |\n`;
          }

        markdownContent += "\n";
      }
    });

    if (!markdownContent.trim()) {
      markdownContent = "# 空文档\n\n该Excel文件中没有可读取的数据。\n";
    } else {
      // 添加文档标题
      const title = filename.replace(/\.(xlsx?|csv)$/i, "");
      markdownContent = `# ${title}\n\n${markdownContent}`;
    }

    // 转换为Buffer并编码为base64
    const markdownBuffer = Buffer.from(markdownContent, "utf8");
    const base64Blob = markdownBuffer.toString("base64");

    // 生成输出文件名
    const outputFilename = filename.replace(/\.(xlsx?|csv)$/i, ".md");

    return [
      {
        filename: outputFilename,
        blob: base64Blob,
      },
    ];
  } catch (error) {
    throw new Error(`Excel转换失败: ${error.message}`);
  }
}

/**
 * 格式化单元格数据
 * @param {any} cellValue - 单元格值
 * @returns {string} - 格式化后的字符串
 */
function formatCellValue(cellValue) {
  if (cellValue === undefined || cellValue === null) {
    return "";
  }

  // 处理日期
  if (cellValue instanceof Date) {
    return cellValue.toLocaleDateString();
  }

  // 处理数字
  if (typeof cellValue === "number") {
    // 如果是整数，直接返回
    if (Number.isInteger(cellValue)) {
      return cellValue.toString();
    }
    // 如果是小数，保留适当的精度
    return Number(cellValue.toFixed(6)).toString();
  }

  // 处理布尔值
  if (typeof cellValue === "boolean") {
    return cellValue ? "TRUE" : "FALSE";
  }

  // 转换为字符串并转义Markdown特殊字符
  return String(cellValue)
    .replace(/\|/g, "\\|")
    .replace(/\n/g, "<br>")
    .replace(/\r/g, "");
}

// main 测试，支持命令行参数
async function main() {
  const file = fs.readFileSync("./test.xlsx");
  const data = await excelToMarkdown(file, "test.xlsx");
  fs.writeFileSync("./test.md", Buffer.from(data[0].blob, "base64"));
  //   console.log(data);
}

if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}
