import { createClient } from "redis";

let redisClient;

const createRedisClient = async () => {
  redisClient = createClient({
    url: process.env.REDIS_URL,
    password: process.env.REDIS_PASSWORD,
  });

  redisClient.on("error", (err) => console.error("Redis Client Error", err));
  redisClient.on("connect", () => console.log("Redis Client Connected"));

  await redisClient.connect();
  return redisClient;
};

export { createRedisClient };
