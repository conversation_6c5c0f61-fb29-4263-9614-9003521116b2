import z from "zod";
import axios from "axios";

// 工具函数：获取百度地图 API Key
function getBaiduMapApiKey() {
  const apiKey = "6a9MdCFds7L7mYep15JAZ3QSuvBC1jcW";
  if (!apiKey) {
    throw new Error("BAIDU_MAP_API_KEY environment variable is not set");
  }
  return apiKey;
}

// 百度地图工具定义
const baiduMapTools = [
  {
    name: "baidu_map_geocode",
    description: "地理编码服务",
    inputSchema: {
      address: z
        .string()
        .describe(
          '待解析的地址（最多支持84个字节。可以输入两种样式的值，分别是：1、标准的结构化地址信息，如北京市海淀区上地十街十号【推荐，地址结构越完整，解析精度越高】2、支持"*路与*路交叉口"描述方式，如北一环路和阜阳路的交叉路口第二种方式并不总是有返回结果，只有当地址库中存在该地址描述时才有返回。）'
        ),
    },
    async handler(userId, { address }) {
      try {
        const apiKey = getBaiduMapApiKey();
        const url = new URL("https://api.map.baidu.com/geocoding/v3/");
        url.searchParams.append("address", address);
        url.searchParams.append("ak", apiKey);
        url.searchParams.append("output", "json");
        url.searchParams.append("from", "node_mcp");

        const response = await axios.get(url.toString());
        const data = response.data;

        if (data.status !== 0) {
          throw new Error(`Geocoding failed: ${data.message || data.status}`);
        }

        return {
          content: [
            {
              type: "text",
              text: JSON.stringify(
                {
                  location: data.result.location,
                  precise: data.result.precise,
                  confidence: data.result.confidence,
                  comprehension: data.result.comprehension,
                  level: data.result.level,
                },
                null,
                2
              ),
            },
          ],
        };
      } catch (error) {
        throw new Error(`Geocoding failed: ${error.message}`);
      }
    },
  },
  {
    name: "baidu_map_reverse_geocode",
    description: "全球逆地理编码",
    inputSchema: {
      latitude: z.number().describe("Latitude coordinate"),
      longitude: z.number().describe("Longitude coordinate"),
    },
    async handler(userId, { latitude, longitude }) {
      try {
        const apiKey = getBaiduMapApiKey();
        const url = new URL("https://api.map.baidu.com/reverse_geocoding/v3/");
        url.searchParams.append("location", `${latitude},${longitude}`);
        url.searchParams.append("extensions_poi", "1");
        url.searchParams.append("ak", apiKey);
        url.searchParams.append("output", "json");
        url.searchParams.append("from", "node_mcp");

        const response = await axios.get(url.toString());
        const data = response.data;

        if (data.status !== 0) {
          throw new Error(
            `Reverse geocoding failed: ${data.message || data.status}`
          );
        }

        return {
          content: [
            {
              type: "text",
              text: JSON.stringify(
                {
                  place_id: data.result.pois[0]
                    ? data.result.pois[0].uid
                    : null,
                  location: data.result.location,
                  formatted_address: data.result.formatted_address,
                  formatted_address_poi: data.result.formatted_address_poi,
                  business: data.result.business,
                  business_info: data.result.business_info,
                  addressComponent: data.result.addressComponent,
                  edz: data.result.edz,
                  pois: data.result.pois,
                  roads: data.result.roads,
                  poiRegions: data.result.poiRegions,
                  sematic_description: data.result.sematic_description,
                  cityCode: data.result.cityCode,
                },
                null,
                2
              ),
            },
          ],
        };
      } catch (error) {
        throw new Error(`Reverse geocoding failed: ${error.message}`);
      }
    },
  },
  {
    name: "baidu_map_search_places",
    description: "地点检索服务（包括城市检索、圆形区域检索、多边形区域检索）",
    inputSchema: {
      query: z.string().describe("检索关键字"),
      region: z.string().optional().describe("检索行政区划区域"),
      bounds: z.string().optional().describe("检索多边形区域"),
      location: z
        .string()
        .optional()
        .describe("圆形区域检索中心点，不支持多个点"),
    },
    async handler(userId, { query, region, bounds, location }) {
      try {
        const apiKey = getBaiduMapApiKey();
        const url = new URL("https://api.map.baidu.com/place/v2/search");
        url.searchParams.append("query", query);
        url.searchParams.append("ak", apiKey);
        url.searchParams.append("output", "json");
        url.searchParams.append("from", "node_mcp");

        if (region) {
          url.searchParams.append("region", region);
        }
        if (bounds) {
          url.searchParams.append("bounds", bounds);
        }
        if (location) {
          url.searchParams.append("location", location);
        }

        const response = await axios.get(url.toString());
        const data = response.data;

        if (data.status !== 0) {
          throw new Error(
            `Place search failed: ${data.message || data.status}`
          );
        }

        const places = data.results || data.result || [];

        return {
          content: [
            {
              type: "text",
              text: JSON.stringify(
                {
                  result_type: data.result_type,
                  query_type: data.query_type,
                  results: places.map((place) => ({
                    name: place.name,
                    location: place.location,
                    address: place.address,
                    province: place.province,
                    city: place.city,
                    area: place.area,
                    street_id: place.street_id,
                    telephone: place.telephone,
                    detail: place.detail,
                    uid: place.uid,
                  })),
                },
                null,
                2
              ),
            },
          ],
        };
      } catch (error) {
        throw new Error(`Place search failed: ${error.message}`);
      }
    },
  },
  {
    name: "baidu_map_place_details",
    description: "地点详情检索服务",
    inputSchema: {
      uid: z.string().describe("poi的uid"),
      scope: z
        .string()
        .optional()
        .describe(
          "检索结果详细程度。取值为1 或空，则返回基本信息；取值为2，返回检索POI详细信息"
        ),
    },
    async handler(userId, { uid, scope }) {
      try {
        const apiKey = getBaiduMapApiKey();
        const url = new URL("https://api.map.baidu.com/place/v2/detail");
        url.searchParams.append("uid", uid);
        url.searchParams.append("ak", apiKey);
        url.searchParams.append("output", "json");
        url.searchParams.append("from", "node_mcp");

        if (scope) {
          url.searchParams.append("scope", scope);
        }

        const response = await axios.get(url.toString());
        const data = response.data;

        if (data.status !== 0) {
          throw new Error(
            `Place details request failed: ${data.message || data.status}`
          );
        }

        return {
          content: [
            {
              type: "text",
              text: JSON.stringify(
                {
                  uid: data.result.uid,
                  name: data.result.name,
                  location: data.result.location,
                  address: data.result.address,
                  province: data.result.province,
                  city: data.result.city,
                  area: data.result.area,
                  street_id: data.result.street_id,
                  detail: data.result.detail,
                  ...("detail_info" in data.result
                    ? {
                        detail_info: data.result.detail_info,
                      }
                    : {}),
                },
                null,
                2
              ),
            },
          ],
        };
      } catch (error) {
        throw new Error(`Place details request failed: ${error.message}`);
      }
    },
  },
  {
    name: "baidu_map_distance_matrix",
    description: "计算多个出发地和目的地的距离和路线用时",
    inputSchema: {
      origins: z.array(z.string()).describe("起点的纬度,经度。"),
      destinations: z.array(z.string()).describe("终点的纬度,经度。"),
      mode: z
        .enum(["driving", "walking", "riding", "motorcycle"])
        .optional()
        .describe(
          "路线类型，可选值：driving（驾车）、walking（步行）、riding（骑行）、motorcycle（摩托车）"
        ),
    },
    async handler(userId, { origins, destinations, mode = "driving" }) {
      try {
        const apiKey = getBaiduMapApiKey();
        const url = new URL("https://api.map.baidu.com/routematrix/v2/" + mode);
        url.searchParams.append("origins", origins.join("|"));
        url.searchParams.append("destinations", destinations.join("|"));
        url.searchParams.append("ak", apiKey);
        url.searchParams.append("output", "json");
        url.searchParams.append("from", "node_mcp");

        const response = await axios.get(url.toString());
        const data = response.data;

        if (data.status !== 0) {
          throw new Error(
            `Distance matrix request failed: ${data.msg || data.status}`
          );
        }

        return {
          content: [
            {
              type: "text",
              text: JSON.stringify(
                {
                  results: data.result.map((row) => ({
                    elements: {
                      duration: row.duration,
                      distance: row.distance,
                    },
                  })),
                },
                null,
                2
              ),
            },
          ],
        };
      } catch (error) {
        throw new Error(`Distance matrix request failed: ${error.message}`);
      }
    },
  },
  {
    name: "baidu_map_directions",
    description: "路线规划服务， 计算出发地到目的地的距离、路线用时、路线方案",
    inputSchema: {
      origin: z
        .string()
        .describe(
          "起点经纬度，格式为：纬度,经度；小数点后不超过6位，40.056878,116.30815"
        ),
      destination: z
        .string()
        .describe(
          "终点经纬度，格式为：纬度,经度；小数点后不超过6位，40.056878,116.30815"
        ),
      mode: z
        .enum(["driving", "walking", "riding", "transit"])
        .optional()
        .describe(
          "路线规划类型，可选值：driving（驾车）、walking（步行）、riding（骑行）、transit（公交）"
        ),
    },
    async handler(userId, { origin, destination, mode = "driving" }) {
      try {
        const apiKey = getBaiduMapApiKey();
        const url = new URL(
          "https://api.map.baidu.com/directionlite/v1/" + mode
        );
        url.searchParams.append("origin", origin);
        url.searchParams.append("destination", destination);
        url.searchParams.append("ak", apiKey);
        url.searchParams.append("from", "node_mcp");

        const response = await axios.get(url.toString());
        const data = response.data;

        if (data.status !== 0) {
          throw new Error(
            `Directions request failed: ${data.msg || data.status}`
          );
        }

        return {
          content: [
            {
              type: "text",
              text: JSON.stringify(
                {
                  routes: data.result.routes.map((route) => ({
                    distance: route.distance,
                    duration: route.duration,
                    steps: route.steps.map((step) => ({
                      instructions: step.instruction,
                    })),
                  })),
                },
                null,
                2
              ),
            },
          ],
        };
      } catch (error) {
        throw new Error(`Directions request failed: ${error.message}`);
      }
    },
  },
  {
    name: "baidu_map_weather",
    description:
      "通过行政区划代码或者经纬度坐标获取实时天气信息和未来5天天气预报",
    inputSchema: {
      districtId: z
        .string()
        .optional()
        .describe("行政区划代码（适用于区、县级别）"),
      location: z
        .string()
        .optional()
        .describe("经纬度，经度在前纬度在后，逗号分隔，格式如116.404,39.915"),
    },
    async handler(userId, { districtId, location }) {
      try {
        const apiKey = getBaiduMapApiKey();
        const url = new URL("https://api.map.baidu.com/weather/v1/");
        url.searchParams.append("data_type", "all");
        url.searchParams.append("coordtype", "bd09ll");
        url.searchParams.append("ak", apiKey);
        url.searchParams.append("from", "node_mcp");

        if (location) {
          url.searchParams.append("location", location);
        }
        if (districtId) {
          url.searchParams.append("district_id", districtId);
        }

        const response = await axios.get(url.toString());
        const data = response.data;

        if (data.status !== 0) {
          throw new Error(
            `Weather search failed: ${data.message || data.status}`
          );
        }

        return {
          content: [
            {
              type: "text",
              text: JSON.stringify(
                {
                  location: data.result.location,
                  now: data.result.now,
                  forecasts: data.result.forecasts,
                  forecast_hours: data.result.forecast_hours,
                  indexes: data.result.indexes,
                  alerts: data.result.alerts,
                },
                null,
                2
              ),
            },
          ],
        };
      } catch (error) {
        throw new Error(`Weather search failed: ${error.message}`);
      }
    },
  },
  {
    name: "baidu_map_ip_location",
    description: "通过IP地址获取位置信息",
    inputSchema: {
      ip: z.string().describe("IP地址"),
    },
    async handler(userId, { ip }) {
      try {
        const apiKey = getBaiduMapApiKey();
        const url = new URL("https://api.map.baidu.com/location/ip");
        url.searchParams.append("ip", ip);
        url.searchParams.append("coor", "bd09ll");
        url.searchParams.append("ak", apiKey);
        url.searchParams.append("from", "node_mcp");

        const response = await axios.get(url.toString());
        const data = response.data;

        if (data.status !== 0) {
          throw new Error(
            `IP address search failed: ${data.message || data.status}`
          );
        }

        return {
          content: [
            {
              type: "text",
              text: JSON.stringify(
                {
                  formatted_address: data.address,
                  address_detail: data.content.address_detail,
                  point: data.content.point,
                },
                null,
                2
              ),
            },
          ],
        };
      } catch (error) {
        throw new Error(`IP address search failed: ${error.message}`);
      }
    },
  },
  {
    name: "baidu_map_road_traffic",
    description:
      "根据城市和道路名称查询具体道路的实时拥堵评价和拥堵路段、拥堵距离、拥堵趋势等信息",
    inputSchema: {
      roadName: z.string().optional().describe("道路名称"),
      city: z.string().optional().describe("城市名称"),
      bounds: z
        .string()
        .optional()
        .describe(
          "矩形区域，左下角和右上角的经纬度坐标点，坐标对间使用;号分隔，格式为：纬度,经度;纬度,经度，如39.912078,116.464303;39.918276,116.475442"
        ),
      vertexes: z
        .string()
        .optional()
        .describe(
          "多边形边界点，经纬度顺序为：纬度,经度； 顶点顺序需按逆时针排列, 格式如vertexes=39.910528,116.472926;39.918276,116.475442;39.916671,116.459056;39.912078,116.464303"
        ),
      center: z
        .string()
        .optional()
        .describe("中心点坐标，如39.912078,116.464303"),
      radius: z.number().optional().describe("查询半径，单位：米"),
    },
    async handler(
      userId,
      { roadName, city, bounds, vertexes, center, radius }
    ) {
      try {
        const apiKey = getBaiduMapApiKey();
        const url = new URL("https://api.map.baidu.com");

        if (roadName && city) {
          url.pathname = "/traffic/v1/road";
          url.searchParams.append("road_name", roadName);
          url.searchParams.append("city", city);
        }
        if (bounds) {
          url.pathname = "/traffic/v1/bound";
          url.searchParams.append("bounds", bounds);
        }
        if (vertexes) {
          url.pathname = "/traffic/v1/polygon";
          url.searchParams.append("vertexes", vertexes);
        }
        if (center && radius) {
          url.pathname = "/traffic/v1/around";
          url.searchParams.append("center", center);
          url.searchParams.append("radius", String(radius));
        }

        url.searchParams.append("ak", apiKey);
        url.searchParams.append("from", "node_mcp");

        const response = await axios.get(url.toString());
        const data = response.data;

        if (data.status !== 0) {
          throw new Error(
            `Road traffic search failed: ${data.message || data.status}`
          );
        }

        return {
          content: [
            {
              type: "text",
              text: JSON.stringify(
                {
                  description: data.description,
                  evaluation: data.evaluation,
                  road_traffic: data.road_traffic,
                },
                null,
                2
              ),
            },
          ],
        };
      } catch (error) {
        throw new Error(`Road traffic search failed: ${error.message}`);
      }
    },
  },
  {
    name: "baidu_map_poi_extract",
    description: "POI智能标注",
    inputSchema: {
      textContent: z.string().describe("描述POI的文本内容"),
    },
    async handler(userId, { textContent }) {
      try {
        const apiKey = getBaiduMapApiKey();
        const submitUrl = "https://api.map.baidu.com/api_mark/v1/submit";
        const params = new URLSearchParams();
        params.append("text_content", textContent);
        params.append("id", "75274677"); // 设备id
        params.append("msg_type", "text");
        params.append("ak", apiKey);
        params.append("from", "node_mcp");

        const submitResponse = await axios.post(submitUrl, params.toString(), {
          headers: {
            "Content-Type": "application/x-www-form-urlencoded",
          },
        });
        const submitData = submitResponse.data;

        if (submitData.status !== 0) {
          throw new Error(
            `Mark submit failed: ${submitData.message || submitData.status}`
          );
        }

        const url = "https://api.map.baidu.com/api_mark/v1/result";
        const mapId = submitData.result.map_id;
        params.delete("text_content");
        params.delete("msg_type");
        params.append("map_id", mapId);

        // 每1s轮询查找数据，等待时间最长20s
        const maxTime = 20 * 1000; // 20s
        const intervalTime = 1000; // 1s
        let elapsedTime = 0;

        async function checkResult() {
          const response = await axios.post(url, params.toString(), {
            headers: {
              "Content-Type": "application/x-www-form-urlencoded",
            },
          });
          const data = response.data;
          const result = data.result;
          if (result) {
            return data;
          }
          return null;
        }

        let data = await checkResult();
        while (!data && elapsedTime < maxTime) {
          await new Promise((resolve) => setTimeout(resolve, intervalTime));
          elapsedTime += intervalTime;
          data = await checkResult();
        }

        if (!data) {
          throw new Error("POI result is null");
        }

        return {
          content: [
            {
              type: "text",
              text: JSON.stringify(
                {
                  jumpUrl: data.result.data[0].link.jump_url,
                  title: data.result.data[0].link.title,
                  desc: data.result.data[0].link.desc,
                  image: data.result.data[0].link.image,
                  poi: data.result.data[0].link.poi,
                },
                null,
                2
              ),
            },
          ],
        };
      } catch (error) {
        throw new Error(`POI extract failed: ${error.message}`);
      }
    },
  },
];

export { baiduMapTools };
