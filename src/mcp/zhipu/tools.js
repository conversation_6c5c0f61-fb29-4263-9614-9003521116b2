import z from "zod";
import axios from "axios";

// 工具函数：获取智谱API Key
function getZhipuApiKey() {
  const apiKey = "11dcd4229aea4fbfbd74f35a84ab3b55.4oHlvwPZxdCfYQ1x"; 
  if (!apiKey || apiKey === "YOUR_API_KEY") {
    throw new Error("ZHIPU_API_KEY environment variable is not set");
  }
  return apiKey;
}

// 智谱GLM-4.1V-Thinking-Flash 工具定义
const zhipuTools = [
  {
    name: "zhipu_glm_url",
    description:
      "调用GLM-4.1V-Thinking-Flash视觉语言模型，理解URL图片内容。",
    inputSchema: {
      image_url: z.string().describe("图片URL，支持JPG和PNG图片"),
      text: z
        .string()
        .default("请仔细描述图片，并以markdown格式输出。如果是文档类图片，请提取其中的文字内容并保持原有格式。")
        .describe("用户希望模型对图片执行的指令，必填"),
    },
    async handler(userId, params) {
      try {
        const apiKey = getZhipuApiKey();
        const url = "https://open.bigmodel.cn/api/paas/v4/chat/completions";
        const headers = {
          "Content-Type": "application/json",
          Authorization: `Bearer ${apiKey}`,
        };
        const body = {
          messages: [
            {
              role: "user",
              content: [
                {
                  type: "image_url",
                  image_url: { url: params.image_url },
                },
                {
                  type: "text",
                  text: params.text,
                },
              ],
            },
          ],
          model: "glm-4.1v-thinking-flash",
        };
        const response = await axios.post(url, body, { headers });
        const data = response.data;
        if (!data.choices || !data.choices.length) {
          throw new Error("No choices returned from Zhipu API");
        }
        // 返回思维链和最终内容
        return {
          content: [
            {
              type: "text",
              text:
                (data.choices[0].message.reasoning_content
                  ? `<Think>\n${data.choices[0].message.reasoning_content}\n</Think>\n\n`
                  : "") + (data.choices[0].message.content || ""),
            },
          ],
        };
      } catch (error) {
        throw new Error(
          `Zhipu GLM-4.1V-Thinking-Flash API 调用失败: ${
            error.response?.data?.msg || error.message
          }`
        );
      }
    },
  },
];

export default zhipuTools;
