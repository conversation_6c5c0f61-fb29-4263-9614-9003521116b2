import z from "zod";
import { AlphaPaiClient } from "./alphapaiClient.js";

// AlphaPai tool definitions
const alphapaiTools = [
  {
    name: "alphapai_question_answer",
    description: "Get AI answers or search from AlphaPai",
    inputSchema: {
      question: z.string().describe("Question content or search content(required)"),
      options: z
        .object({
          isWebSearch: z
            .boolean()
            .optional()
            .describe("Enable web search (default: false)"),
          isDeepReasoning: z
            .boolean()
            .optional()
            .describe("Enable deep reasoning (default: false)"),
        })
        .optional()
        .describe("Optional parameters for Q&A"),
    },
    async handler(userId, { question, options = {}}) {
      try {
        const client = new AlphaPaiClient(userId);
        const response = await client.qaText(question, options);
        
        let references = "";
        for (let i = 0; i < response.references.length; i++) {
          const ref = response.references[i];
          if (ref.type) {
            references += `[${ref.rank}]${ref.title + " "}${ref.type + " "}${ref.teamName || ref.instShortName || "" + " "}${ref.publishDate + " "}${ref.url}\n`;
          }
        }
        return {
          content: [
            {
              type: "text",
              text: `Q&A Response:\nQuestion: ${question}\nAnswer: \n${JSON.stringify(response.answer)}\nReferences: \n${references}`,
            },
          ],
        };
      } catch (error) {
        throw new Error(`Q&A request failed: ${error.message}`);
      }
    },
  },
];

export { alphapaiTools };
