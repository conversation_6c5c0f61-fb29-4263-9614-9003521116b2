import axios from "axios";
import crypto from "crypto";

/**
 * AlphaPai API 客户端
 * 实现接口签名验证和响应处理
 */
class AlphaPaiClient {
  constructor(userId) {
    // 初始化配置
    this.appId = "j9hwqsWqBdvIydp56oyDLZE1";
    this.appSecret = "OWM0NDQ2YTE0N2MxYWFmMjUzNjMyMmM5MTZhOTkxYmM5ZDlmYjI3OA==";
    this.baseURL = "https://api-test.rabyte.cn/";
    this.timeout = 600000;
    this.retryAttempts = 3;
    this.retryDelay = 1000;

    // 创建HTTP客户端
    this.client = this.createAxiosInstance();
    this.userInfo = {
      userCode: userId,
      userName: userId,
      roleName: "基金经理",
      roleCode: "5",
    };
  }

  /**
   * 创建axios实例
   */
  createAxiosInstance() {
    const instance = axios.create({
      baseURL: this.baseURL,
      timeout: this.timeout,
      headers: {
        "Content-Type": "application/json",
      },
    });

    // 请求拦截器 - 添加签名
    instance.interceptors.request.use(
      (request) => {
        return this.addSignature(request);
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // 响应拦截器 - 处理响应
    instance.interceptors.response.use(
      (response) => {
        return this.handleResponse(response);
      },
      (error) => {
        return this.handleError(error);
      }
    );

    return instance;
  }

  /**
   * 生成SHA256签名
   * @param {string} data - 要签名的数据
   * @returns {string} 签名字符串
   */
  generateSignature(data) {
    return crypto
      .createHmac("sha256", this.appSecret)
      .update(data, "utf8")
      .digest("hex");
  }

  addSignature(request) {
    let bodyString = "";

    // 获取请求body的原始字符串
    if (request.data) {
      if (typeof request.data === "string") {
        bodyString = request.data;
      } else if (typeof request.data === "object") {
        bodyString = JSON.stringify(request.data);
      }
    }

    // 生成签名
    const signature = this.generateSignature(bodyString);

    // 添加必要的headers
    request.headers = {
      ...request.headers,
      "app-agent": this.appId,
      sign: signature,
    };

    return request;
  }

  /**
   * 处理响应
   * @param {object} response - axios响应对象
   * @returns {object} 处理后的响应
   */
  handleResponse(response) {
    // 如果是流式响应，直接返回流对象
    if (response.config.responseType === "stream") {
      return response;
    } else {
      // 处理JSON响应
      let data;

      // 如果response.data是string且以"data: "开头，则移除前缀并解析JSON
      if (
        typeof response.data === "string" &&
        response.data.startsWith("data:")
      ) {
        data = JSON.parse(response.data.substring(5));
      } else if (typeof response.data === "object") {
        data = response.data;
      }

      const { code, message, data: responseData } = data;

      // 根据返回码处理响应
      return this.processResponseCode(code, message, responseData);
    }
  }

  /**
   * 处理响应状态码
   * @param {number} code - 响应码
   * @param {string} message - 响应消息
   * @param {any} responseData - 响应数据
   * @returns {object} 处理后的响应
   */
  processResponseCode(code, message, responseData) {
    const errorMessages = {
      401000: "Authentication failed",
      42900: "Flow limit exceeded",
      500303: "Parameter error",
      500000: "Internal server error",
    };

    if (code === 200000) {
      return {
        success: true,
        code,
        message,
        data: responseData,
      };
    }

    const errorMessage = errorMessages[code]
      ? `${errorMessages[code]}: ${message}`
      : `Unknown error: ${message}`;

    throw new AlphaPaiError(errorMessage, code);
  }

  /**
   * 处理错误
   * @param {object} error - axios错误对象
   * @returns {Promise} 拒绝的Promise
   */
  handleError(error) {
    if (error instanceof AlphaPaiError) {
      return Promise.reject(error);
    }

    if (error.response) {
      // 服务器响应了错误状态码
      const { status, data } = error.response;
      return Promise.reject(
        new AlphaPaiError(
          `HTTP ${status}: ${data?.message || error.message}`,
          data?.code || status
        )
      );
    } else if (error.request) {
      // 请求已发送但没有收到响应
      return Promise.reject(
        new AlphaPaiError(
          "Network error: No response received",
          "NETWORK_ERROR"
        )
      );
    } else {
      // 请求配置错误
      return Promise.reject(
        new AlphaPaiError("Request error: " + error.message, "REQUEST_ERROR")
      );
    }
  }

  /**
   * 文本校验API - 校验文本是否会触发AI问答
   * @param {string} query - 待校验文本
   * @returns {Promise<object>} 返回校验结果
   * @example
   * const result = await client.checkText('飞行汽车');
   * // 返回: { intentFlag: false, entities: ['飞行汽车'] }
   */
  async checkText(query) {
    if (!query || typeof query !== "string") {
      throw new AlphaPaiError(
        "Parameter error: query is required and must be a string",
        500303
      );
    }

    const response = await this.client.request({
      method: "POST",
      url: "/alpha/open-api/v1/paipai/text/check",
      data: { query },
    });

    return response.data;
  }

  /**
   * 文本问答API - 通过输入文本问题获取AI回答
   * @param {string} question - 问题内容 (必须)
   * @param {object} options - 可选参数
   * @returns {Promise<object>} 返回问答结果，如果isStream为true则返回流对象
   * @example
   * // 普通响应
   * const result = await client.qaText(
   *   '为什么美光比海力士在hbm的市占率低？',
   * );
   *
   * // 流式响应
   * const streamResult = await client.qaText(
   *   '为什么美光比海力士在hbm的市占率低？',
   *   { isStream: true }
   * );
   *
   * // 处理流式数据
   * streamResult.on('data', (chunk) => {
   *   console.log(chunk.toString());
   * });
   *
   * streamResult.on('end', () => {
   *   console.log('Stream ended');
   * });
   */
  async qaText(question, options = {}) {
    // 参数验证
    if (!question || typeof question !== "string") {
      throw new AlphaPaiError(
        "Parameter error: question is required and must be a string",
        500303
      );
    }

    // 构建请求体
    const requestBody = {
      question,
      // 默认参数
      isAutoRoute: true,
      isStream: false,
      isWebSearch: options.isWebSearch ?? false,
      isDeepReasoning: options.isDeepReasoning ?? false,
      userInfo: this.userInfo,
    };

    // 发送请求
    const config = requestBody.isStream ? { responseType: "stream" } : {};

    try {
      const response = await this.client.request({
        method: "POST",
        url: "/alpha/open-api/v1/paipai/qa-text",
        data: requestBody,
        ...config,
      });

      return response.data;
    } catch (error) {
      // 增强错误信息
      if (error instanceof AlphaPaiError) {
        throw error;
      }

      throw new AlphaPaiError(
        `QA Text request failed: ${error.message}`,
        error.code || "REQUEST_FAILED"
      );
    }
  }
}

/**
 * AlphaPai 自定义错误类
 */
class AlphaPaiError extends Error {
  constructor(message, code, originalError = null) {
    super(message);
    this.name = "AlphaPaiError";
    this.code = code;
    this.originalError = originalError;
    this.timestamp = new Date().toISOString();

    // 保持堆栈跟踪
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, AlphaPaiError);
    }
  }

  /**
   * 获取错误的JSON表示
   * @returns {object}
   */
  toJSON() {
    return {
      name: this.name,
      message: this.message,
      code: this.code,
      timestamp: this.timestamp,
      stack: this.stack,
    };
  }
}

export { AlphaPaiClient, AlphaPaiError };
