import hashlib
import hmac
from datetime import datetime

import requests
import time
import json


def calculate_hmac(content, app_secret):
    key = app_secret.encode('utf-8')
    content_bytes = content.encode('utf-8')
    hmac_digest = hmac.new(key, content_bytes, hashlib.sha256).hexdigest()
    return hmac_digest


# 提取日志工具函数
def format_log(prefix, message):
    current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    return f"({current_time}) {prefix} : {message}"


# Variables for the request
URL = 'https://api-test.rabyte.cn/alpha/open-api/v1/paipai/qa-text'
# URL = 'http://localhost:10999/alpha/open-api/v1/paipai/qa-text'

question = "请梳理下国内各车厂智能驾驶的研发进度"

DATA = {
    "question": question,
    "userInfo": {
        "userCode": "001010",
        "userName": "张力帆",
        "roleName": "基金经理",
        "roleCode": 5
    },
    "questionId": "",
    "isStream": True,
    "isAutoRoute": False
}

DATA_JSON = json.dumps(DATA, ensure_ascii=False)

app_id = "j9hwqsWqBdvIydp56oyDLZE1"
app_secret = "OWM0NDQ2YTE0N2MxYWFmMjUzNjMyMmM5MTZhOTkxYmM5ZDlmYjI3OA=="
sign = calculate_hmac(DATA_JSON, app_secret)

HEADERS = {
    'sign': sign,
    'app-agent': app_id,
    'Content-Type': 'application/json',
    'Accept': '*/*',
    'Connection': 'keep-alive'
}

# Start time
start_time = int(time.time() * 1000)
print(f"(时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}) 开始时间: {start_time} 毫秒")

# Execute the request and process the response
response = requests.post(URL, headers=HEADERS, data=DATA_JSON.encode('utf-8'), stream=True)

end_time = 0

# 新增缓冲区处理机制
buffer = ''

for chunk in response.iter_content(chunk_size=4096):  # 增大chunk_size减少碎片
    if not chunk:
        continue

    try:
        # 使用errors='replace'避免解码中断
        chunk_str = chunk.decode('utf-8', errors='replace')
    except UnicodeDecodeError as e:
        print(format_log("解码错误", f"{e} - 原始数据: {chunk.hex()}"))
        continue

    buffer += chunk_str

    # 按事件分隔符处理（假设服务端使用\n\n分隔）
    while '\n\n' in buffer:
        event, buffer = buffer.split('\n\n', 1)

        # 处理单个事件
        event = event.strip()
        if not event.startswith('data: '):
            continue

        line = event[6:].strip()  # 移除data:前缀
        if not line:
            continue

        try:
            data = json.loads(line)
            if isinstance(data, dict):
                print(format_log("解析后的数据", data))
        except Exception as e:
            print(format_log("JSON解析异常", f"{type(e).__name__}: {str(e)}"))
