import { AlphaPaiClient, AlphaPaiError } from "./alphapaiClient.js";

// ==================== 测试配置 ====================
const TEST_CONFIG = {
  delays: {
    checkText: 1000,
    qaText: 2000,
  },
  retry: {
    maxAttempts: 2,
    delayBetweenAttempts: 1000,
  },
  testCases: {
    checkText: [
      {
        query: "飞行汽车",
        description: "普通名词测试",
      },
      //   {
      //     query: "今天天气怎么样？",
      //     description: "简单问句测试",
      //   },
      //   {
      //     query: "什么是人工智能？",
      //     description: "知识问答测试",
      //   },
    ],
    qaText: [
      {
        question: "为什么美光比海力士在hbm的市占率低？",
        options: {},
        description: "基础问答测试",
      },
      //   {
      //     question: "对行业的影响呢？",
      //     options: {
      //       isWebSearch: true,
      //       isDeepReasoning: false,
      //     },
      //     description: "上下文问答测试",
      //   },
      //   {
      //     question: "什么是人工智能？",
      //     options: {
      //       isWebSearch: true,
      //       isDeepReasoning: false,
      //     },
      //     description: "联网搜索测试",
      //   },
    ],
  },
};

// ==================== 工具函数 ====================
/**
 * 延迟函数
 * @param {number} ms - 延迟毫秒数
 */
const delay = (ms) => new Promise((resolve) => setTimeout(resolve, ms));

/**
 * 带重试的执行函数
 * @param {Function} fn - 要执行的异步函数
 * @param {number} maxAttempts - 最大重试次数
 * @param {number} delayMs - 重试间隔
 */
async function executeWithRetry(fn, maxAttempts = 2, delayMs = 1000) {
  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      return await fn();
    } catch (error) {
      if (attempt === maxAttempts) {
        throw error;
      }
      console.log(`    ⚠️ 第${attempt}次尝试失败，${delayMs}ms后重试...`);
      await delay(delayMs);
    }
  }
}

// ==================== 测试函数 ====================
/**
 * 测试文本校验API
 * @param {AlphaPaiClient} client - 客户端实例
 * @returns {object} 测试统计
 */
async function testCheckTextAPI(client) {
  console.log("【1】开始测试文本校验API...\n");

  const testCases = TEST_CONFIG.testCases.checkText;

  for (let i = 0; i < testCases.length; i++) {
    const { query, description } = testCases[i];
    console.log(`校验案例 ${i + 1}: "${query}" (${description})`);

    try {
      const result = await executeWithRetry(
        () => client.checkText(query),
        TEST_CONFIG.retry.maxAttempts,
        TEST_CONFIG.retry.delayBetweenAttempts
      );
      console.log(result);
    } catch (error) {
      console.error("error", `请求失败: ${error.message}`, {
        错误码: error.code || "未知",
      });
    }

    if (i < testCases.length - 1) {
      await delay(TEST_CONFIG.delays.checkText);
    }
  }
}

/**
 * 测试文本问答API
 * @param {AlphaPaiClient} client - 客户端实例
 * @returns {object} 测试统计
 */
async function testQATextAPI(client) {
  console.log("【2】开始测试文本问答API...\n");

  const testCases = TEST_CONFIG.testCases.qaText;

  for (let i = 0; i < testCases.length; i++) {
    const { question, options, description } = testCases[i];
    console.log(`问答案例 ${i + 1}: "${question}" (${description})`);

    if (options.isWebSearch) {
      console.log(`  - 开启联网搜索: ${options.isWebSearch}`);
    }
    if (options.isDeepReasoning) {
      console.log(`  - 深度推理: ${options.isDeepReasoning}`);
    }

    try {
      const response = await executeWithRetry(
        () => client.qaText(question, options),
        TEST_CONFIG.retry.maxAttempts,
        TEST_CONFIG.retry.delayBetweenAttempts
      );

      let block = "";
      const references = [];

      if ("stream" === response.config?.responseType) {
        // 处理流式数据，只输出 answer 属性
        response.on("data", (chunk) => {
          let chunkString = chunk.toString();
          block += chunkString;
          const lines = block.split("\n\n");
          for (let i = 0; i < lines.length - 1; i++) {
            const line = lines[i];
            if (line.startsWith("data:")) {
              const data = JSON.parse(line.substring(5));
              if (data.code === 200000 && data.data?.answer) {
                process.stdout.write(data.data.answer);
                if (data.data.references) {
                  references.push(...data.data.references);
                }
              }
              if (data.code === 42900) {
                console.error("error", `请求失败: ${data.message}`, {
                  错误码: data.code || "未知",
                });
                process.exit(1);
                break;
              }
            }
          }
          block = lines[lines.length - 1];
          // console.log(block);
        });

        response.on("end", () => {
          process.stdout.write("\n");
          for (let i = 0; i < references.length; i++) {
            const ref = references[i];
            console.log(
              `[${ref.rank}]${ref.title + " "}${ref.type + " "}${
                ref.teamName || ref.instShortName || "" + " "
              }${ref.publishDate + " "}${ref.url}`
            );
          }
        });
      } else {
        console.log(response.answer);
        for (let i = 0; i < response.references.length; i++) {
          const ref = response.references[i];
          if (ref.type) {
            console.log(
              `[${ref.rank}]${ref.title + " "}${ref.type + " "}${
                ref.teamName || ref.instShortName || "" + " "
              }${ref.publishDate + " "}${ref.url}`
            );
          }
        }
      }
    } catch (error) {
      console.error("error", `请求失败: ${error.message}`, {
        错误码: error.code || "未知",
      });
    }

    if (i < testCases.length - 1) {
      await delay(TEST_CONFIG.delays.qaText);
    }
  }
}

/**
 * 初始化客户端并显示基本信息
 * @returns {AlphaPaiClient} 客户端实例
 */
function initializeClient() {
  console.log("=== AlphaPai API 测试 ===\n");

  const client = new AlphaPaiClient("liuliuliu");
  console.log("✓ 客户端创建成功");
  console.log(`- App ID: ${client.appId}`);
  console.log(`- Base URL: ${client.baseURL}`);
  console.log(`- 超时时间: ${client.timeout}ms\n`);

  return client;
}

/**
 * 测试文本校验和问答功能的主方法
 */
async function main() {
  try {
    // 初始化客户端
    const client = initializeClient();

    // 执行测试
    await testCheckTextAPI(client);
    await testQATextAPI(client);
  } catch (error) {
    console.error("❌ 测试初始化失败:", error.message);
    if (error.code) {
      console.error(`错误码: ${error.code}`);
    }
  }
}

/**
 * 显示帮助信息
 */
function showHelp() {
  console.log("=== AlphaPai API 客户端测试工具 ===\n");
  console.log("使用方法:");
  console.log("  node test.js [选项]\n");
  console.log("选项:");
  console.log("  (无参数)    - 运行完整测试");
  console.log("  help        - 显示此帮助信息\n");
  console.log("示例:");
  console.log("  node test.js");
}

// 如果直接运行此文件，则执行测试
import { fileURLToPath } from 'url';
import { dirname } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Check if this is the main module being executed
if (import.meta.url === `file://${process.argv[1]}`) {
  const testType = process.argv[2];

  switch (testType) {
    case "help":
    case "--help":
    case "-h":
      showHelp();
      break;
    default:
      main().catch(console.error);
      break;
  }
}
