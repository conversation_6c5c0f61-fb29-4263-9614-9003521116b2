import z from "zod";
import { getJsnowballToken } from "./dao.js";
import * as jsnowball from "./index.js";
import { formatResponse } from "./dictionary.js";

// 工具函数：获取 Jsnowball 客户端并设置token
async function getJsnowballClient(userId) {
  const token = await getJsnowballToken(userId);
  if (!token) {
    throw new Error("Jsnowball token not found for user");
  }
  jsnowball.setToken(token);
  return jsnowball;
}



// Jsnowball tool definitions
const jsnowballTools = [
  // 实时数据
  {
    name: "jsnowball_realtime_quote",
    description: "获取股票实时行情数据",
    inputSchema: {
      symbols: z.string().describe("股票代码，多个股票用逗号分隔，例如：SH000001,SZ399001"),
    },
    async handler(userId, { symbols }) {
      try {
        const client = await getJsnowballClient(userId);
        const response = await client.quotec(symbols);
        return { content: formatResponse(response, 'quotec') };
      } catch (error) {
        throw new Error(`获取实时行情失败: ${error.message}`);
      }
    },
  },
  {
    name: "jsnowball_quote_detail",
    description: "获取股票详细行情信息",
    inputSchema: {
      symbol: z.string().describe("股票代码，例如：SH000001"),
    },
    async handler(userId, { symbol }) {
      try {
        const client = await getJsnowballClient(userId);
        const response = await client.quoteDetail(symbol);
        return { content: formatResponse(response, 'quoteDetail') };
      } catch (error) {
        throw new Error(`获取详细行情失败: ${error.message}`);
      }
    },
  },
  {
    name: "jsnowball_pankou",
    description: "获取股票盘口数据",
    inputSchema: {
      symbol: z.string().describe("股票代码，例如：SH000001"),
    },
    async handler(userId, { symbol }) {
      try {
        const client = await getJsnowballClient(userId);
        const response = await client.pankou(symbol);
        return { content: formatResponse(response, 'pankou') };
      } catch (error) {
        throw new Error(`获取盘口数据失败: ${error.message}`);
      }
    },
  },
  {
    name: "jsnowball_kline",
    description: "获取股票K线数据",
    inputSchema: {
      symbol: z.string().describe("股票代码，例如：SH000001"),
      period: z.enum(["day", "week", "month", "quarter", "year"]).optional().describe("K线周期：day(日线), week(周线), month(月线), quarter(季线), year(年线)"),
      count: z.number().optional().describe("获取数量，默认284"),
    },
    async handler(userId, { symbol, period = "day", count = 284 }) {
      try {
        const client = await getJsnowballClient(userId);
        const response = await client.kline(symbol, period, count);
        return { content: formatResponse(response, 'kline') };
      } catch (error) {
        throw new Error(`获取K线数据失败: ${error.message}`);
      }
    },
  },

  // 财务数据
  {
    name: "jsnowball_finance_cash_flow",
    description: "获取股票现金流量表数据",
    inputSchema: {
      symbol: z.string().describe("股票代码，例如：SH000001"),
      isAnnals: z.number().optional().describe("是否年报：0(季报), 1(年报)"),
      count: z.number().optional().describe("获取数量，默认10"),
    },
    async handler(userId, { symbol, isAnnals = 0, count = 10 }) {
      try {
        const client = await getJsnowballClient(userId);
        const response = await client.cashFlow(symbol, isAnnals, count);
        return { content: formatResponse(response, 'cashFlow') };
      } catch (error) {
        throw new Error(`获取现金流量表失败: ${error.message}`);
      }
    },
  },
  {
    name: "jsnowball_finance_balance",
    description: "获取股票资产负债表数据",
    inputSchema: {
      symbol: z.string().describe("股票代码，例如：SH000001"),
      isAnnals: z.number().optional().describe("是否年报：0(季报), 1(年报)"),
      count: z.number().optional().describe("获取数量，默认10"),
    },
    async handler(userId, { symbol, isAnnals = 0, count = 10 }) {
      try {
        const client = await getJsnowballClient(userId);
        const response = await client.balance(symbol, isAnnals, count);
        return { content: formatResponse(response, 'balance') };
      } catch (error) {
        throw new Error(`获取资产负债表失败: ${error.message}`);
      }
    },
  },
  {
    name: "jsnowball_finance_income",
    description: "获取股票利润表数据",
    inputSchema: {
      symbol: z.string().describe("股票代码，例如：SH000001"),
      isAnnals: z.number().optional().describe("是否年报：0(季报), 1(年报)"),
      count: z.number().optional().describe("获取数量，默认10"),
    },
    async handler(userId, { symbol, isAnnals = 0, count = 10 }) {
      try {
        const client = await getJsnowballClient(userId);
        const response = await client.income(symbol, isAnnals, count);
        return { content: formatResponse(response, 'income') };
      } catch (error) {
        throw new Error(`获取利润表失败: ${error.message}`);
      }
    },
  },
  {
    name: "jsnowball_finance_indicator",
    description: "获取股票财务指标数据",
    inputSchema: {
      symbol: z.string().describe("股票代码，例如：SH000001"),
      isAnnals: z.number().optional().describe("是否年报：0(季报), 1(年报)"),
      isDetail: z.boolean().optional().describe("是否详细数据"),
      count: z.number().optional().describe("获取数量，默认10"),
    },
    async handler(userId, { symbol, isAnnals = 0, isDetail = false, count = 10 }) {
      try {
        const client = await getJsnowballClient(userId);
        const response = await client.indicator(symbol, isAnnals, isDetail, count);
        return { content: formatResponse(response, 'indicator') };
      } catch (error) {
        throw new Error(`获取财务指标失败: ${error.message}`);
      }
    },
  },
  {
    name: "jsnowball_finance_business",
    description: "获取股票业务数据",
    inputSchema: {
      symbol: z.string().describe("股票代码，例如：SH000001"),
      isAnnals: z.number().optional().describe("是否年报：0(季报), 1(年报)"),
      count: z.number().optional().describe("获取数量，默认10"),
    },
    async handler(userId, { symbol, isAnnals = 0, count = 10 }) {
      try {
        const client = await getJsnowballClient(userId);
        const response = await client.business(symbol, isAnnals, count);
        return { content: formatResponse(response, 'business') };
      } catch (error) {
        throw new Error(`获取业务数据失败: ${error.message}`);
      }
    },
  },

  // 资金流向数据
  {
    name: "jsnowball_capital_flow",
    description: "获取股票资金流向数据",
    inputSchema: {
      symbol: z.string().describe("股票代码，例如：SH000001"),
    },
    async handler(userId, { symbol }) {
      try {
        const client = await getJsnowballClient(userId);
        const response = await client.capitalFlow(symbol);
        return { content: formatResponse(response, 'capitalFlow') };
      } catch (error) {
        throw new Error(`获取资金流向失败: ${error.message}`);
      }
    },
  },
  {
    name: "jsnowball_capital_margin",
    description: "获取股票融资融券数据",
    inputSchema: {
      symbol: z.string().describe("股票代码，例如：SH000001"),
      page: z.number().optional().describe("页码，默认1"),
      size: z.number().optional().describe("每页数量，默认180"),
    },
    async handler(userId, { symbol, page = 1, size = 180 }) {
      try {
        const client = await getJsnowballClient(userId);
        const response = await client.margin(symbol, page, size);
        return { content: formatResponse(response, 'margin') };
      } catch (error) {
        throw new Error(`获取融资融券数据失败: ${error.message}`);
      }
    },
  },
  {
    name: "jsnowball_capital_blocktrans",
    description: "获取股票大宗交易数据",
    inputSchema: {
      symbol: z.string().describe("股票代码，例如：SH000001"),
      page: z.number().optional().describe("页码，默认1"),
      size: z.number().optional().describe("每页数量，默认30"),
    },
    async handler(userId, { symbol, page = 1, size = 30 }) {
      try {
        const client = await getJsnowballClient(userId);
        const response = await client.blocktrans(symbol, page, size);
        return { content: formatResponse(response, 'blocktrans') };
      } catch (error) {
        throw new Error(`获取大宗交易数据失败: ${error.message}`);
      }
    },
  },
  {
    name: "jsnowball_capital_assort",
    description: "获取股票资金分类数据",
    inputSchema: {
      symbol: z.string().describe("股票代码，例如：SH000001"),
    },
    async handler(userId, { symbol }) {
      try {
        const client = await getJsnowballClient(userId);
        const response = await client.capitalAssort(symbol);
        return { content: formatResponse(response, 'capitalAssort') };
      } catch (error) {
        throw new Error(`获取资金分类数据失败: ${error.message}`);
      }
    },
  },
  {
    name: "jsnowball_capital_history",
    description: "获取股票资金历史数据",
    inputSchema: {
      symbol: z.string().describe("股票代码，例如：SH000001"),
      count: z.number().optional().describe("获取数量，默认20"),
    },
    async handler(userId, { symbol, count = 20 }) {
      try {
        const client = await getJsnowballClient(userId);
        const response = await client.capitalHistory(symbol, count);
        return { content: formatResponse(response, "capitalHistory") };
      } catch (error) {
        throw new Error(`获取资金历史数据失败: ${error.message}`);
      }
    },
  },

  // F10数据
  {
    name: "jsnowball_f10_skholderchg",
    description: "获取股票股东变化数据",
    inputSchema: {
      symbol: z.string().describe("股票代码，例如：SH000001"),
    },
    async handler(userId, { symbol }) {
      try {
        const client = await getJsnowballClient(userId);
        const response = await client.skholderchg(symbol);
        return { content: formatResponse(response, "skholderchg") };
      } catch (error) {
        throw new Error(`获取股东变化数据失败: ${error.message}`);
      }
    },
  },
  {
    name: "jsnowball_f10_skholder",
    description: "获取股票股东数据",
    inputSchema: {
      symbol: z.string().describe("股票代码，例如：SH000001"),
    },
    async handler(userId, { symbol }) {
      try {
        const client = await getJsnowballClient(userId);
        const response = await client.skholder(symbol);
        return { content: formatResponse(response, "skholder") };
      } catch (error) {
        throw new Error(`获取股东数据失败: ${error.message}`);
      }
    },
  },
  {
    name: "jsnowball_f10_industry",
    description: "获取股票行业数据",
    inputSchema: {
      symbol: z.string().describe("股票代码，例如：SH000001"),
    },
    async handler(userId, { symbol }) {
      try {
        const client = await getJsnowballClient(userId);
        const response = await client.industry(symbol);
        return { content: formatResponse(response, "industry") };
      } catch (error) {
        throw new Error(`获取行业数据失败: ${error.message}`);
      }
    },
  },
  {
    name: "jsnowball_f10_holders",
    description: "获取股票持有人数据",
    inputSchema: {
      symbol: z.string().describe("股票代码，例如：SH000001"),
    },
    async handler(userId, { symbol }) {
      try {
        const client = await getJsnowballClient(userId);
        const response = await client.holders(symbol);
        return { content: formatResponse(response, "holders") };
      } catch (error) {
        throw new Error(`获取持有人数据失败: ${error.message}`);
      }
    },
  },
  {
    name: "jsnowball_f10_bonus",
    description: "获取股票分红数据",
    inputSchema: {
      symbol: z.string().describe("股票代码，例如：SH000001"),
      page: z.number().optional().describe("页码，默认1"),
      size: z.number().optional().describe("每页数量，默认10"),
    },
    async handler(userId, { symbol, page = 1, size = 10 }) {
      try {
        const client = await getJsnowballClient(userId);
        const response = await client.bonus(symbol, page, size);
        return { content: formatResponse(response, "bonus") };
      } catch (error) {
        throw new Error(`获取分红数据失败: ${error.message}`);
      }
    },
  },
  {
    name: "jsnowball_f10_org_holding_change",
    description: "获取股票机构持股变化数据",
    inputSchema: {
      symbol: z.string().describe("股票代码，例如：SH000001"),
    },
    async handler(userId, { symbol }) {
      try {
        const client = await getJsnowballClient(userId);
        const response = await client.orgHoldingChange(symbol);
        return { content: formatResponse(response, 'orgHoldingChange') };
      } catch (error) {
        throw new Error(`获取机构持股变化数据失败: ${error.message}`);
      }
    },
  },
  {
    name: "jsnowball_f10_industry_compare",
    description: "获取股票行业对比数据",
    inputSchema: {
      symbol: z.string().describe("股票代码，例如：SH000001"),
    },
    async handler(userId, { symbol }) {
      try {
        const client = await getJsnowballClient(userId);
        const response = await client.industryCompare(symbol);
        return { content: formatResponse(response, 'industryCompare') };
      } catch (error) {
        throw new Error(`获取行业对比数据失败: ${error.message}`);
      }
    },
  },
  {
    name: "jsnowball_f10_business_analysis",
    description: "获取股票业务分析数据",
    inputSchema: {
      symbol: z.string().describe("股票代码，例如：SH000001"),
    },
    async handler(userId, { symbol }) {
      try {
        const client = await getJsnowballClient(userId);
        const response = await client.businessAnalysis(symbol);
        return { content: formatResponse(response, 'businessAnalysis') };
      } catch (error) {
        throw new Error(`获取业务分析数据失败: ${error.message}`);
      }
    },
  },
  {
    name: "jsnowball_f10_shareschg",
    description: "获取股票股本变化数据",
    inputSchema: {
      symbol: z.string().describe("股票代码，例如：SH000001"),
      count: z.number().optional().describe("获取数量，默认5"),
    },
    async handler(userId, { symbol, count = 5 }) {
      try {
        const client = await getJsnowballClient(userId);
        const response = await client.shareschg(symbol, count);
        return { content: formatResponse(response, 'shareschg') };
      } catch (error) {
        throw new Error(`获取股本变化数据失败: ${error.message}`);
      }
    },
  },
  {
    name: "jsnowball_f10_top_holders",
    description: "获取股票前十大股东数据",
    inputSchema: {
      symbol: z.string().describe("股票代码，例如：SH000001"),
      circula: z.number().optional().describe("流通股本，默认1"),
    },
    async handler(userId, { symbol, circula = 1 }) {
      try {
        const client = await getJsnowballClient(userId);
        const response = await client.topHolders(symbol, circula);
        return { content: formatResponse(response, 'topHolders') };
      } catch (error) {
        throw new Error(`获取前十大股东数据失败: ${error.message}`);
      }
    },
  },
  {
    name: "jsnowball_f10_main_indicator",
    description: "获取股票主要指标数据",
    inputSchema: {
      symbol: z.string().describe("股票代码，例如：SH000001"),
    },
    async handler(userId, { symbol }) {
      try {
        const client = await getJsnowballClient(userId);
        const response = await client.mainIndicator(symbol);
        return { content: formatResponse(response, 'mainIndicator') };
      } catch (error) {
        throw new Error(`获取主要指标数据失败: ${error.message}`);
      }
    },
  },

  // 报告数据
  {
    name: "jsnowball_report",
    description: "获取股票最新报告数据",
    inputSchema: {
      symbol: z.string().describe("股票代码，例如：SH000001"),
    },
    async handler(userId, { symbol }) {
      try {
        const client = await getJsnowballClient(userId);
        const response = await client.report(symbol);
        return { content: formatResponse(response) };
      } catch (error) {
        throw new Error(`获取最新报告数据失败: ${error.message}`);
      }
    },
  },
  {
    name: "jsnowball_earning_forecast",
    description: "获取股票盈利预测数据",
    inputSchema: {
      symbol: z.string().describe("股票代码，例如：SH000001"),
    },
    async handler(userId, { symbol }) {
      try {
        const client = await getJsnowballClient(userId);
        const response = await client.earningforecast(symbol);
        return { content: formatResponse(response) };
      } catch (error) {
        throw new Error(`获取盈利预测数据失败: ${error.message}`);
      }
    },
  },

  // 基金数据
  {
    name: "jsnowball_fund_detail",
    description: "获取基金详细信息",
    inputSchema: {
      fundCode: z.string().describe("基金代码，例如：000001"),
    },
    async handler(userId, { fundCode }) {
      try {
        const client = await getJsnowballClient(userId);
        const response = await client.fundDetail(fundCode);
        return { content: formatResponse(response, 'fundDetail') };
      } catch (error) {
        throw new Error(`获取基金详细信息失败: ${error.message}`);
      }
    },
  },
  {
    name: "jsnowball_fund_info",
    description: "获取基金基本信息",
    inputSchema: {
      fundCode: z.string().describe("基金代码，例如：000001"),
    },
    async handler(userId, { fundCode }) {
      try {
        const client = await getJsnowballClient(userId);
        const response = await client.fundInfo(fundCode);
        return { content: formatResponse(response, 'fundInfo') };
      } catch (error) {
        throw new Error(`获取基金信息失败: ${error.message}`);
      }
    },
  },
  {
    name: "jsnowball_fund_growth",
    description: "获取基金成长数据",
    inputSchema: {
      fundCode: z.string().describe("基金代码，例如：000001"),
      day: z.string().optional().describe("天数，默认ty"),
    },
    async handler(userId, { fundCode, day = 'ty' }) {
      try {
        const client = await getJsnowballClient(userId);
        const response = await client.fundGrowth(fundCode, day);
        return { content: formatResponse(response, 'fundGrowth') };
      } catch (error) {
        throw new Error(`获取基金成长数据失败: ${error.message}`);
      }
    },
  },
  {
    name: "jsnowball_fund_nav_history",
    description: "获取基金净值历史数据",
    inputSchema: {
      fundCode: z.string().describe("基金代码，例如：000001"),
      page: z.number().optional().describe("页码，默认1"),
      size: z.number().optional().describe("每页数量，默认10"),
    },
    async handler(userId, { fundCode, page = 1, size = 10 }) {
      try {
        const client = await getJsnowballClient(userId);
        const response = await client.fundNavHistory(fundCode, page, size);
        return { content: formatResponse(response, 'fundNavHistory') };
      } catch (error) {
        throw new Error(`获取基金净值历史数据失败: ${error.message}`);
      }
    },
  },
  {
    name: "jsnowball_fund_achievement",
    description: "获取基金业绩数据",
    inputSchema: {
      fundCode: z.string().describe("基金代码，例如：000001"),
    },
    async handler(userId, { fundCode }) {
      try {
        const client = await getJsnowballClient(userId);
        const response = await client.fundAchievement(fundCode);
        return { content: formatResponse(response, 'fundAchievement') };
      } catch (error) {
        throw new Error(`获取基金业绩数据失败: ${error.message}`);
      }
    },
  },
  {
    name: "jsnowball_fund_asset",
    description: "获取基金资产数据",
    inputSchema: {
      fundCode: z.string().describe("基金代码，例如：000001"),
    },
    async handler(userId, { fundCode }) {
      try {
        const client = await getJsnowballClient(userId);
        const response = await client.fundAsset(fundCode);
        return { content: formatResponse(response, 'fundAsset') };
      } catch (error) {
        throw new Error(`获取基金资产数据失败: ${error.message}`);
      }
    },
  },
  {
    name: "jsnowball_fund_manager",
    description: "获取基金经理数据",
    inputSchema: {
      fundCode: z.string().describe("基金代码，例如：000001"),
      postStatus: z.number().optional().describe("职位状态，默认1"),
    },
    async handler(userId, { fundCode, postStatus = 1 }) {
      try {
        const client = await getJsnowballClient(userId);
        const response = await client.fundManager(fundCode, postStatus);
        return { content: formatResponse(response, 'fundManager') };
      } catch (error) {
        throw new Error(`获取基金经理数据失败: ${error.message}`);
      }
    },
  },
  {
    name: "jsnowball_fund_trade_date",
    description: "获取基金交易日期数据",
    inputSchema: {
      fundCode: z.string().describe("基金代码，例如：000001"),
    },
    async handler(userId, { fundCode }) {
      try {
        const client = await getJsnowballClient(userId);
        const response = await client.fundTradeDate(fundCode);
        return { content: formatResponse(response, 'fundTradeDate') };
      } catch (error) {
        throw new Error(`获取基金交易日期数据失败: ${error.message}`);
      }
    },
  },
  {
    name: "jsnowball_fund_derived",
    description: "获取基金衍生数据",
    inputSchema: {
      fundCode: z.string().describe("基金代码，例如：000001"),
    },
    async handler(userId, { fundCode }) {
      try {
        const client = await getJsnowballClient(userId);
        const response = await client.fundDerived(fundCode);
        return { content: formatResponse(response, 'fundDerived') };
      } catch (error) {
        throw new Error(`获取基金衍生数据失败: ${error.message}`);
      }
    },
  },

  // 债券数据
  {
    name: "jsnowball_convertible_bond",
    description: "获取可转债数据",
    inputSchema: {
      symbol: z.string().describe("债券代码，例如：SH113000"),
    },
    async handler(userId, { symbol }) {
      try {
        const client = await getJsnowballClient(userId);
        const response = await client.convertibleBond(symbol);
        return { content: formatResponse(response, 'convertibleBond') };
      } catch (error) {
        throw new Error(`获取可转债数据失败: ${error.message}`);
      }
    },
  },

  // 指数数据
  {
    name: "jsnowball_index_basic_info",
    description: "获取指数基本信息",
    inputSchema: {
      symbols: z.string().describe("指数代码，例如：SH000001"),
    },
    async handler(userId, { symbols }) {
      try {
        const client = await getJsnowballClient(userId);
        const response = await client.indexBasicInfo(symbols);
        return { content: formatResponse(response, 'indexBasicInfo') };
      } catch (error) {
        throw new Error(`获取指数基本信息失败: ${error.message}`);
      }
    },
  },
  {
    name: "jsnowball_index_details_data",
    description: "获取指数详细数据",
    inputSchema: {
      symbols: z.string().describe("指数代码，例如：SH000001"),
    },
    async handler(userId, { symbols }) {
      try {
        const client = await getJsnowballClient(userId);
        const response = await client.indexDetailsData(symbols);
        return { content: formatResponse(response, 'indexDetailsData') };
      } catch (error) {
        throw new Error(`获取指数详细数据失败: ${error.message}`);
      }
    },
  },
  {
    name: "jsnowball_index_weight_top10",
    description: "获取指数权重前10数据",
    inputSchema: {
      symbols: z.string().describe("指数代码，例如：SH000001"),
    },
    async handler(userId, { symbols }) {
      try {
        const client = await getJsnowballClient(userId);
        const response = await client.indexWeightTop10(symbols);
        return { content: formatResponse(response) };
      } catch (error) {
        throw new Error(`获取指数权重前10数据失败: ${error.message}`);
      }
    },
  },
  {
    name: "jsnowball_index_perf_7",
    description: "获取指数7天表现数据",
    inputSchema: {
      symbols: z.string().describe("指数代码，例如：SH000001"),
    },
    async handler(userId, { symbols }) {
      try {
        const client = await getJsnowballClient(userId);
        const response = await client.indexPerf7(symbols);
        return { content: formatResponse(response, 'indexPerf') };
      } catch (error) {
        throw new Error(`获取指数7天表现数据失败: ${error.message}`);
      }
    },
  },
  {
    name: "jsnowball_index_perf_30",
    description: "获取指数30天表现数据",
    inputSchema: {
      symbols: z.string().describe("指数代码，例如：SH000001"),
    },
    async handler(userId, { symbols }) {
      try {
        const client = await getJsnowballClient(userId);
        const response = await client.indexPerf30(symbols);
        return { content: formatResponse(response) };
      } catch (error) {
        throw new Error(`获取指数30天表现数据失败: ${error.message}`);
      }
    },
  },
  {
    name: "jsnowball_index_perf_90",
    description: "获取指数90天表现数据",
    inputSchema: {
      symbols: z.string().describe("指数代码，例如：SH000001"),
    },
    async handler(userId, { symbols }) {
      try {
        const client = await getJsnowballClient(userId);
        const response = await client.indexPerf90(symbols);
        return { content: formatResponse(response) };
      } catch (error) {
        throw new Error(`获取指数90天表现数据失败: ${error.message}`);
      }
    },
  },

  // 用户数据
  {
    name: "jsnowball_watch_list",
    description: "获取用户自选股列表",
    inputSchema: {},
    async handler(userId) {
      try {
        const client = await getJsnowballClient(userId);
        const response = await client.watchList();
        return { content: formatResponse(response, 'watchList') };
      } catch (error) {
        throw new Error(`获取自选股列表失败: ${error.message}`);
      }
    },
  },
  {
    name: "jsnowball_watch_stock",
    description: "获取用户自选股详情",
    inputSchema: {
      id: z.string().describe("自选股ID"),
    },
    async handler(userId, { id }) {
      try {
        const client = await getJsnowballClient(userId);
        const response = await client.watchStock(id);
        return { content: formatResponse(response, 'watchStock') };
      } catch (error) {
        throw new Error(`获取自选股详情失败: ${error.message}`);
      }
    },
  },

  // 搜索和建议
  {
    name: "jsnowball_suggest_stock",
    description: "搜索股票建议",
    inputSchema: {
      keyword: z.string().describe("搜索关键词，例如：贵州茅台"),
    },
    async handler(userId, { keyword }) {
      try {
        const client = await getJsnowballClient(userId);
        const response = await client.suggestStock(keyword);
        return { content: formatResponse(response, 'suggestStock') };
      } catch (error) {
        throw new Error(`搜索股票失败: ${error.message}`);
      }
    },
  },

  // 港股通数据
  {
    name: "jsnowball_northbound_shareholding_sh",
    description: "获取沪股通持股数据",
    inputSchema: {
      txtDate: z.string().optional().describe("日期，格式：YYYY-MM-DD"),
    },
    async handler(userId, { txtDate = null }) {
      try {
        const client = await getJsnowballClient(userId);
        const response = await client.northboundShareholdingSh(txtDate);
        return { content: formatResponse(response, 'northboundShareholdingSh') };
      } catch (error) {
        throw new Error(`获取沪股通持股数据失败: ${error.message}`);
      }
    },
  },
  {
    name: "jsnowball_northbound_shareholding_sz",
    description: "获取深股通持股数据",
    inputSchema: {
      txtDate: z.string().optional().describe("日期，格式：YYYY-MM-DD"),
    },
    async handler(userId, { txtDate = null }) {
      try {
        const client = await getJsnowballClient(userId);
        const response = await client.northboundShareholdingSz(txtDate);
        return { content: formatResponse(response, 'northboundShareholdingSz') };
      } catch (error) {
        throw new Error(`获取深股通持股数据失败: ${error.message}`);
      }
    },
  },
  {
    name: "jsnowball_tran_code",
    description: "港股代码转换为A股代码",
    inputSchema: {
      code: z.string().describe("港股代码，例如：70001"),
    },
    async handler(userId, { code }) {
      try {
        const client = await getJsnowballClient(userId);
        const response = client.tranCode(code);
        return { content: formatResponse(response, 'tranCode') };
      } catch (error) {
        throw new Error(`港股代码转换失败: ${error.message}`);
      }
    },
  },

  // 组合数据
  {
    name: "jsnowball_cube_nav_daily",
    description: "获取组合每日净值数据",
    inputSchema: {
      symbol: z.string().describe("组合代码"),
      since: z.number().optional().describe("起始时间戳，单位: 毫秒"),
      until: z.number().optional().describe("结束时间戳，单位: 毫秒"),
    },
    async handler(userId, { symbol, since, until }) {
      try {
        const client = await getJsnowballClient(userId);
        const response = await client.navDaily(symbol, since, until);
        return { content: formatResponse(response, 'navDaily') };
      } catch (error) {
        throw new Error(`获取组合每日净值数据失败: ${error.message}`);
      }
    },
  },
  {
    name: "jsnowball_cube_rebalancing_history",
    description: "获取组合调仓历史数据",
    inputSchema: {
      symbol: z.string().describe("组合代码"),
      count: z.number().optional().describe("获取数量，默认20"),
      page: z.number().optional().describe("页码，默认1"),
    },
    async handler(userId, { symbol, count = 20, page = 1 }) {
      try {
        const client = await getJsnowballClient(userId);
        const response = await client.rebalancingHistory(symbol, count, page);
        return { content: formatResponse(response, 'rebalancingHistory') };
      } catch (error) {
        throw new Error(`获取组合调仓历史数据失败: ${error.message}`);
      }
    },
  },
  {
    name: "jsnowball_cube_rebalancing_current",
    description: "获取组合当前调仓数据",
    inputSchema: {
      symbol: z.string().describe("组合代码"),
    },
    async handler(userId, { symbol }) {
      try {
        const client = await getJsnowballClient(userId);
        const response = await client.rebalancingCurrent(symbol);
        return { content: formatResponse(response, 'rebalancingCurrent') };
      } catch (error) {
        throw new Error(`获取组合当前调仓数据失败: ${error.message}`);
      }
    },
  },
  {
    name: "jsnowball_cube_quote_current",
    description: "获取组合当前行情数据",
    inputSchema: {
      symbol: z.string().describe("组合代码"),
    },
    async handler(userId, { symbol }) {
      try {
        const client = await getJsnowballClient(userId);
        const response = await client.quoteCurrent(symbol);
        return { content: formatResponse(response, 'quoteCurrent') };
      } catch (error) {
        throw new Error(`获取组合当前行情数据失败: ${error.message}`);
      }
    },
  },
];

export { jsnowballTools }; 