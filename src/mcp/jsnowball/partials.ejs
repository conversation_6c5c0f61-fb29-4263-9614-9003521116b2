<div class="bg-white shadow rounded-lg p-6 mt-8">
    <h2 class="text-lg font-medium text-gray-900 mb-4"><%= t('jsnowball:title') %></h2>
    <div id="jsnowballTokenSection" class="space-y-4">
        <% if (jsnowball.token) { %>
            <div class="bg-gray-50 p-4 rounded-md">
                <p class="text-sm text-gray-600 mb-2"><%= t('jsnowball:yourToken') %></p>
                <div class="flex items-center space-x-2">
                    <code class="bg-gray-100 px-2 py-1 rounded text-sm font-mono" id="jsnowballTokenCode"><%= jsnowball.token %></code>
                    <button onclick="copyJsnowballToken()" class="text-indigo-600 hover:text-indigo-900 text-sm">
                        <%= t('jsnowball:copy') %>
                    </button>
                </div>
            </div>
            <button onclick="deleteJsnowballToken()" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                <%= t('jsnowball:delete') %>
            </button>
        <% } else { %>
            <form id="setJsnowballTokenForm" class="flex flex-col sm:flex-row gap-2 items-start sm:items-center">
                <input type="text" id="jsnowballTokenInput" name="token" placeholder="<%= t('jsnowball:tokenPlaceholder') %>" class="border px-2 py-1 rounded text-sm w-full sm:w-96" required />
                <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    <%= t('jsnowball:set') %>
                </button>
            </form>
            <div class="text-sm text-gray-500">
                <p><%= t('jsnowball:tokenInstructions.title') %></p>
                <p><%= t('jsnowball:tokenInstructions.step1') %></p>
                <p><%= t('jsnowball:tokenInstructions.step2') %></p>
                <p><%= t('jsnowball:tokenInstructions.step3') %></p>
                <p><%= t('jsnowball:tokenInstructions.step4') %></p>
                <p><%= t('jsnowball:tokenInstructions.step5') %></p>
            </div>
        <% } %>
    </div>
</div>

<script>
    async function deleteJsnowballToken() {
        if (!confirm('<%= t("jsnowball:deleteConfirm") %>')) return;
        try {
            const response = await fetch('/mcp/jsnowball/token', { method: 'DELETE' });
            if (response.ok) window.location.reload();
        } catch (error) {
            console.error('Error deleting Jsnowball token:', error);
        }
    }
    function copyJsnowballToken() {
        const token = document.getElementById('jsnowballTokenCode').textContent;
        navigator.clipboard.writeText(token).then(() => {
            alert('<%= t("jsnowball:copySuccess") %>');
        });
    }
    const setJsnowballTokenForm = document.getElementById('setJsnowballTokenForm');
    if (setJsnowballTokenForm) {
        setJsnowballTokenForm.addEventListener('submit', async function(e) {
            e.preventDefault();
            const token = document.getElementById('jsnowballTokenInput').value;
            try {
                const response = await fetch('/mcp/jsnowball/token', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ token })
                });
                if (response.ok) window.location.reload();
            } catch (error) {
                console.error('Error setting Jsnowball token:', error);
            }
        });
    }
</script> 