import { fetch } from './utils.js';
import * as apiRef from './apiRef.js';

const host = "xueqiu.com";

/**
 * 获取组合净值日线数据
 * @param {string} symbol 组合代码
 * @param {number} [since] 起始时间戳（可选，单位: 毫秒，默认为最近1个月）
 * @param {number} [until] 结束时间戳（可选，单位: 毫秒，默认为当前时间）
 */
export async function navDaily(symbol, since, until) {
    const now = Date.now();
    // 默认since为30天前
    if (since === undefined) {
        since = now - 30 * 24 * 60 * 60 * 1000;
    }
    if (until === undefined) {
        until = now;
    }
    let url = apiRef.NAV_DAILY + symbol;
    url += `&since=${since}`;
    url += `&until=${until}`;
    return await fetch(url, host);
}

export async function rebalancingHistory(symbol, count = 20, page = 1) {
    let url = apiRef.REBALANCING_HISTORY + symbol;
    url += '&count=' + count;
    url += '&page=' + page;

    return await fetch(url, host);
}

export async function rebalancingCurrent(symbol) {
    const url = apiRef.REBALANCING_CURRENT + symbol;
    return await fetch(url, host);
}

export async function quoteCurrent(symbol) {
    const url = apiRef.QUOTE_CURRENT + symbol;
    return await fetch(url, host);
}


