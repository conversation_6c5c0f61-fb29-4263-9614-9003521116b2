import express from "express";
import { storeJsnowballToken, deleteJsnowballToken } from "./dao.js";

const router = express.Router();

// Jsnowball Token routes
router.post("/jsnowball/token", async (req, res) => {
  const { token } = req.body;
  if (!token) return res.status(400).json({ error: "Jsnowball Token required" });
  await storeJsnowballToken(req.session.user.id, token);
  res.status(200).json({ token });
});

router.delete("/jsnowball/token", async (req, res) => {
  await deleteJsnowballToken(req.session.user.id);
  res.status(204).send();
});

export default router; 