// Jsnowball 数据格式化工具

// 格式化响应数据的函数
export function formatResponse(response, toolName = null) {
  // 如果是 suggestStock 响应，进行特殊处理
  if (toolName === "suggestStock") {
    return formatSuggestStockResponse(response);
  }

  // 如果是 cashFlow 响应，进行特殊处理
  if (toolName === "cashFlow") {
    return formatCashFlowResponse(response);
  }

  // 如果是 indicator 响应，进行特殊处理
  if (toolName === "indicator") {
    return formatIndicatorResponse(response);
  }

  // 如果是 balance 响应，进行特殊处理
  if (toolName === "balance") {
    return formatBalanceResponse(response);
  }

  // 如果是 income 响应，进行特殊处理
  if (toolName === "income") {
    return formatIncomeResponse(response);
  }

  // 如果是 business 响应，进行特殊处理
  if (toolName === "business") {
    return formatBusinessResponse(response);
  }

  // 如果是 quotec 响应，进行特殊处理
  if (toolName === "quotec") {
    return formatQuotecResponse(response);
  }

  // 如果是 quoteDetail 响应，进行特殊处理
  if (toolName === "quoteDetail") {
    return formatQuoteDetailResponse(response);
  }

  // 如果是 pankou 响应，进行特殊处理
  if (toolName === "pankou") {
    return formatPankouResponse(response);
  }

  // 如果是 kline 响应，进行特殊处理
  if (toolName === "kline") {
    return formatKlineResponse(response);
  }

  // 如果是 margin 响应，进行特殊处理
  if (toolName === "margin") {
    return formatMarginResponse(response);
  }

  // 如果是 blocktrans 响应，进行特殊处理
  if (toolName === "blocktrans") {
    return formatBlocktransResponse(response);
  }

  // 如果是 capitalAssort 响应，进行特殊处理
  if (toolName === "capitalAssort") {
    return formatCapitalAssortResponse(response);
  }

  // 如果是 capitalFlow 响应，进行特殊处理
  if (toolName === "capitalFlow") {
    return formatCapitalFlowResponse(response);
  }

  // 如果是 capitalHistory 响应，进行特殊处理
  if (toolName === "capitalHistory") {
    return formatCapitalHistoryResponse(response);
  }

  // 如果是 skholderchg 响应，进行特殊处理
  if (toolName === "skholderchg") {
    return formatSkholderchgResponse(response);
  }

  // 如果是 skholder 响应，进行特殊处理
  if (toolName === "skholder") {
    return formatSkholderResponse(response);
  }

  // 如果是 industry 响应，进行特殊处理
  if (toolName === "industry") {
    return formatIndustryResponse(response);
  }

  // 如果是 holders 响应，进行特殊处理
  if (toolName === "holders") {
    return formatHoldersResponse(response);
  }

  // 如果是 bonus 响应，进行特殊处理
  if (toolName === "bonus") {
    return formatBonusResponse(response);
  }

  // 如果是 orgHoldingChange 响应，进行特殊处理
  if (toolName === "orgHoldingChange") {
    return formatOrgHoldingChangeResponse(response);
  }

  // 如果是 industryCompare 响应，进行特殊处理
  if (toolName === "industryCompare") {
    return formatIndustryCompareResponse(response);
  }

  // 如果是 businessAnalysis 响应，进行特殊处理
  if (toolName === "businessAnalysis") {
    return formatBusinessAnalysisResponse(response);
  }

  // 如果是 shareschg 响应，进行特殊处理
  if (toolName === "shareschg") {
    return formatShareschgResponse(response);
  }

  // 如果是 topHolders 响应，进行特殊处理
  if (toolName === "topHolders") {
    return formatTopHoldersResponse(response);
  }

  // 如果是 mainIndicator 响应，进行特殊处理
  if (toolName === "mainIndicator") {
    return formatMainIndicatorResponse(response);
  }

  // 如果是 navDaily 响应，进行特殊处理
  if (toolName === "navDaily") {
    return formatNavDailyResponse(response);
  }

  // 如果是 rebalancingHistory 响应，进行特殊处理
  if (toolName === "rebalancingHistory") {
    return formatRebalancingHistoryResponse(response);
  }

  // 如果是 rebalancingCurrent 响应，进行特殊处理
  if (toolName === "rebalancingCurrent") {
    return formatRebalancingCurrentResponse(response);
  }

  // 如果是 quoteCurrent 响应，进行特殊处理
  if (toolName === "quoteCurrent") {
    return formatQuoteCurrentResponse(response);
  }

  // 如果是 fundDetail 响应，进行特殊处理
  if (toolName === "fundDetail") {
    return formatFundDetailResponse(response);
  }

  // 如果是 fundInfo 响应，进行特殊处理
  if (toolName === "fundInfo") {
    return formatFundInfoResponse(response);
  }

  // 如果是 fundGrowth 响应，进行特殊处理
  if (toolName === "fundGrowth") {
    return formatFundGrowthResponse(response);
  }

  // 如果是 fundNavHistory 响应，进行特殊处理
  if (toolName === "fundNavHistory") {
    return formatFundNavHistoryResponse(response);
  }

  // 如果是 fundAchievement 响应，进行特殊处理
  if (toolName === "fundAchievement") {
    return formatFundAchievementResponse(response);
  }

  // 如果是 fundAsset 响应，进行特殊处理
  if (toolName === "fundAsset") {
    return formatFundAssetResponse(response);
  }

  // 如果是 fundManager 响应，进行特殊处理
  if (toolName === "fundManager") {
    return formatFundManagerResponse(response);
  }

  // 如果是 fundTradeDate 响应，进行特殊处理
  if (toolName === "fundTradeDate") {
    return formatFundTradeDateResponse(response);
  }

  // 如果是 fundDerived 响应，进行特殊处理
  if (toolName === "fundDerived") {
    return formatFundDerivedResponse(response);
  }

  // 如果是 convertibleBond 响应，进行特殊处理
  if (toolName === "convertibleBond") {
    return formatConvertibleBondResponse(response);
  }

  // 如果是 indexBasicInfo 响应，进行特殊处理
  if (toolName === "indexBasicInfo") {
    return formatIndexBasicInfoResponse(response);
  }

  // 如果是 indexDetailsData 响应，进行特殊处理
  if (toolName === "indexDetailsData") {
    return formatIndexDetailsDataResponse(response);
  }

  // 如果是 indexPerf7 响应，进行特殊处理
  if (toolName === "indexPerf") {
    return formatIndexPerfResponse(response);
  }

  // 如果是 watchList 响应，进行特殊处理
  if (toolName === "watchList") {
    return formatWatchListResponse(response);
  }

  // 如果是 watchStock 响应，进行特殊处理
  if (toolName === "watchStock") {
    return formatWatchStockResponse(response);
  }

  // 如果是 northboundShareholdingSh 响应，进行特殊处理
  if (toolName === "northboundShareholdingSh") {
    return formatNorthboundShareholdingShResponse(response);
  }

  // 如果是 northboundShareholdingSz 响应，进行特殊处理
  if (toolName === "northboundShareholdingSz") {
    return formatNorthboundShareholdingSzResponse(response);
  }

  // 如果是 tranCode 响应，进行特殊处理
  if (toolName === "tranCode") {
    return formatTranCodeResponse(response);
  }

  // 默认返回 data 字段
  return response.data || response;
}

// 格式化 suggestStock 响应
function formatSuggestStockResponse(response) {
  if (!response.data || !Array.isArray(response.data)) {
    return response;
  }

  const formattedData = response.data.map((item) => ({
    股票代码: item.code,
    股票名称: item.query,
    标签: item.label,
    状态: item.state,
    股票类型: item.stock_type,
    类型: item.type,
  }));

  return {
    搜索结果: formattedData,
    总数: response.meta?.count || 0,
    当前页: response.meta?.page || 1,
    页面大小: response.meta?.size || 0,
    是否有下一页: response.meta?.has_next_page || false,
    查询ID: response.meta?.query_id || null,
  };
}

// 格式化 quotec 响应
function formatQuotecResponse(response) {
  if (!response.data || !Array.isArray(response.data)) {
    return response;
  }

  const formattedData = response.data.map((item) => ({
    股票代码: item.symbol,
    当前价: formatPrice(item.current),
    涨跌幅: formatPercent(item.percent),
    涨跌额: formatPrice(item.chg),
    时间: formatTimestamp(item.timestamp),
    成交量: formatVolume(item.volume),
    成交额: formatAmount(item.amount),
    总市值: formatMarketCap(item.market_capital),
    流通市值: formatMarketCap(item.float_market_capital),
    换手率: formatPercent(item.turnover_rate),
    振幅: formatPercent(item.amplitude),
    开盘价: formatPrice(item.open),
    昨收价: formatPrice(item.last_close),
    最高价: formatPrice(item.high),
    最低价: formatPrice(item.low),
    均价: formatPrice(item.avg_price),
    年内涨跌幅: formatPercent(item.current_year_percent),
    是否交易: item.is_trade ? "是" : "否",
    级别: item.level,
  }));

  return {
    实时行情数据: formattedData,
  };
}

// 格式化 cashFlow 响应
function formatCashFlowResponse(response) {
  if (!response.data) {
    return response;
  }

  const { data } = response;

  // 格式化列表数据
  const formattedList = data.list.map((item) => ({
    报告日期: formatTimestamp(item.report_date),
    报告名称: item.report_name,
    经营活动现金流量: formatCashFlowItem(item.ncf_from_oa),
    投资活动现金流量: formatCashFlowItem(item.ncf_from_ia),
    筹资活动现金流量: formatCashFlowItem(item.ncf_from_fa),
  }));

  return {
    股票名称: data.quote_name,
    货币名称: data.currency_name,
    机构类型: getOrgTypeName(data.org_type),
    最新报告名称: data.last_report_name,
    货币代码: data.currency,
    现金流量数据: formattedList,
  };
}

// 格式化 indicator 响应
function formatIndicatorResponse(response) {
  if (!response.data) {
    return response;
  }

  const { data } = response;

  // 格式化列表数据
  const formattedList = data.list.map((item) => ({
    报告日期: formatTimestamp(item.report_date),
    报告名称: item.report_name,
    平均净资产收益率: formatIndicatorItem(item.avg_roe, "%"),
    每股净资产: formatIndicatorItem(item.np_per_share, "元"),
    每股经营现金流: formatIndicatorItem(item.operate_cash_flow_ps, "元"),
    基本每股收益: formatIndicatorItem(item.basic_eps, "元"),
    资本公积: formatIndicatorItem(item.capital_reserve, "元"),
    每股未分配利润: formatIndicatorItem(item.undistri_profit_ps, "元"),
    总资产净利率: formatIndicatorItem(item.net_interest_of_total_assets, "%"),
    净销售利润率: formatIndicatorItem(item.net_selling_rate, "%"),
  }));

  return {
    股票名称: data.quote_name,
    货币名称: data.currency_name,
    机构类型: getOrgTypeName(data.org_type),
    最新报告名称: data.last_report_name,
    货币代码: data.currency,
    财务指标数据: formattedList,
  };
}

// 格式化 balance 响应
function formatBalanceResponse(response) {
  if (!response.data) {
    return response;
  }

  const { data } = response;

  // 格式化列表数据
  const formattedList = data.list.map((item) => ({
    报告日期: formatTimestamp(item.report_date),
    报告名称: item.report_name,
    总资产: formatBalanceItem(item.total_assets, "亿元"),
    总负债: formatBalanceItem(item.total_liab, "亿元"),
    资产负债率: formatBalanceItem(item.asset_liab_ratio, "%"),
  }));

  return {
    股票名称: data.quote_name,
    货币名称: data.currency_name,
    机构类型: getOrgTypeName(data.org_type),
    最新报告名称: data.last_report_name,
    货币代码: data.currency,
    资产负债表数据: formattedList,
  };
}

// 格式化 income 响应
function formatIncomeResponse(response) {
  if (!response.data) {
    return response;
  }

  const { data } = response;

  // 格式化列表数据
  const formattedList = data.list.map((item) => ({
    报告日期: formatTimestamp(item.report_date),
    报告名称: item.report_name,
    净利润: formatIncomeItem(item.net_profit, "亿元"),
    扣除非经常性损益后的净利润: formatIncomeItem(item.net_profit_atsopc, "亿元"),
    营业总收入: formatIncomeItem(item.total_revenue, "亿元"),
    营业利润: formatIncomeItem(item.op, "亿元"),
  }));

  return {
    股票名称: data.quote_name,
    货币名称: data.currency_name,
    机构类型: getOrgTypeName(data.org_type),
    最新报告名称: data.last_report_name,
    货币代码: data.currency,
    利润表数据: formattedList,
  };
}

// 格式化 business 响应
function formatBusinessResponse(response) {
  if (!response.data) {
    return response;
  }

  const { data } = response;

  // 格式化列表数据
  const formattedList = data.list.map((item) => ({
    报告日期: formatTimestamp(item.report_date),
    报告名称: item.report_name,
    分类列表: formatClassList(item.class_list),
  }));

  return {
    股票名称: data.quote_name,
    产品类型: data.product_type,
    主要经营业务: data.main_operation_business,
    行业: {
      行业名称: data.industry?.ind_name,
      行业代码: data.industry?.ind_code,
    },
    经营范围: data.operation_range,
    产品名称: data.product_name,
    货币代码: data.currency_code,
    业务数据: formattedList,
  };
}

// 格式化 quoteDetail 响应
function formatQuoteDetailResponse(response) {
  if (!response.data) {
    return response;
  }

  const { data } = response;
  const { market, quote, others, tags } = data;

  return {
    市场信息: {
      状态: market.status,
      地区: market.region,
      时区: market.time_zone,
      时区描述: market.time_zone_desc,
      延迟标签: market.delay_tag,
      降级夜盘: market.downgrade_night_session ? "是" : "否",
    },
    股票信息: {
      股票代码: quote.symbol,
      股票名称: quote.name,
      交易所: quote.exchange,
      当前价: formatPrice(quote.current),
      涨跌幅: formatPercent(quote.percent),
      涨跌额: formatPrice(quote.chg),
      开盘价: formatPrice(quote.open),
      昨收价: formatPrice(quote.last_close),
      最高价: formatPrice(quote.high),
      最低价: formatPrice(quote.low),
      均价: formatPrice(quote.avg_price),
      成交量: formatVolume(quote.volume),
      成交额: formatAmount(quote.amount),
      换手率: formatPercent(quote.turnover_rate),
      振幅: formatPercent(quote.amplitude),
      年内涨跌幅: formatPercent(quote.current_year_percent),
      时间: formatTimestamp(quote.timestamp),
    },
    财务指标: {
      每股收益: formatPrice(quote.eps),
      市盈率预测: quote.pe_forecast?.toFixed(2),
      市盈率去年: quote.pe_lyr?.toFixed(2),
      市盈率TTM: quote.pe_ttm?.toFixed(2),
      市净率: quote.pb?.toFixed(2),
      每股净资产: formatPrice(quote.navps),
      股息: formatPrice(quote.dividend),
      股息率: formatPercent(quote.dividend_yield),
      净利润: formatAmount(quote.profit),
      四季度净利润: formatAmount(quote.profit_four),
      预测净利润: formatAmount(quote.profit_forecast),
    },
    股本信息: {
      总股本: formatShares(quote.total_shares),
      流通股本: formatShares(quote.float_shares),
      总市值: formatMarketCap(quote.market_capital),
      流通市值: formatMarketCap(quote.float_market_capital),
    },
    交易信息: {
      量比: quote.volume_ratio?.toFixed(2),
      涨停价: formatPrice(quote.limit_up),
      跌停价: formatPrice(quote.limit_down),
      最小变动单位: formatPrice(quote.tick_size),
      每手股数: quote.lot_size?.toLocaleString() + "股",
      上市日期: formatTimestamp(quote.issue_date),
      质押比例: formatPercent(quote.pledge_ratio),
      商誉占净资产比例: formatPercent(quote.goodwill_in_net_assets),
    },
    状态信息: {
      盈利状态: quote.no_profit_desc,
      VIE状态: quote.is_vie_desc,
      注册制状态: quote.is_registration_desc,
      同股不同权: quote.weighted_voting_rights_desc,
      子类型: quote.sub_type,
      证券状态: quote.security_status,
      锁定期: quote.lock_set,
    },
    其他信息: {
      盘口比例: formatPercent(others?.pankou_ratio),
      创业板开关: others?.cyb_switch ? "开启" : "关闭",
    },
    标签: formatTags(tags),
  };
}

// 格式化 pankou 响应
function formatPankouResponse(response) {
  if (!response.data) {
    return response;
  }

  const { data } = response;

  // 格式化买盘数据
  const buyOrders = [];
  for (let i = 1; i <= 10; i++) {
    const price = data[`bp${i}`];
    const volume = data[`bc${i}`];
    const name = data[`bn${i}`];
    
    if (price !== null && price !== undefined) {
      buyOrders.push({
        价格: formatPrice(price),
        数量: formatVolume(volume),
        名称: name,
      });
    }
  }

  // 格式化卖盘数据
  const sellOrders = [];
  for (let i = 1; i <= 10; i++) {
    const price = data[`sp${i}`];
    const volume = data[`sc${i}`];
    const name = data[`sn${i}`];
    
    if (price !== null && price !== undefined) {
      sellOrders.push({
        价格: formatPrice(price),
        数量: formatVolume(volume),
        名称: name,
      });
    }
  }

  return {
    股票代码: data.symbol,
    当前价: formatPrice(data.current),
    时间: formatTimestamp(data.timestamp),
    买盘: buyOrders,
    卖盘: sellOrders,
    买盘占比: formatPercent(data.buypct),
    卖盘占比: formatPercent(data.sellpct),
    买卖差: formatVolume(data.diff),
    买卖比: formatPercent(data.ratio),
    级别: data.level,
  };
}

// 格式化 kline 响应
function formatKlineResponse(response) {
  if (!response.data) {
    return response;
  }

  const { data } = response;
  const { symbol, column, item } = data;

  // 定义列名映射
  const columnMap = {
    timestamp: "时间",
    volume: "成交量",
    open: "开盘价",
    high: "最高价",
    low: "最低价",
    close: "收盘价",
    chg: "涨跌额",
    percent: "涨跌幅",
    turnoverrate: "换手率",
    amount: "成交额",
    volume_post: "后复权成交量",
    amount_post: "后复权成交额",
    pe: "市盈率",
    pb: "市净率",
    ps: "市销率",
    pcf: "市现率",
    market_capital: "总市值",
    balance: "余额",
    hold_volume_cn: "中资持股数量",
    hold_ratio_cn: "中资持股比例",
    net_volume_cn: "中资净流入",
    hold_volume_hk: "港资持股数量",
    hold_ratio_hk: "港资持股比例",
    net_volume_hk: "港资净流入",
  };

  // 格式化K线数据
  const formattedItems = item.map((row) => {
    const formattedRow = {};
    
    column.forEach((colName, index) => {
      const value = row[index];
      const chineseName = columnMap[colName] || colName;
      
      // 根据字段类型进行格式化
      switch (colName) {
        case 'timestamp':
          formattedRow[chineseName] = formatTimestamp(value);
          break;
        case 'open':
        case 'high':
        case 'low':
        case 'close':
          formattedRow[chineseName] = formatPrice(value);
          break;
        case 'volume':
        case 'volume_post':
        case 'hold_volume_cn':
        case 'hold_volume_hk':
        case 'net_volume_cn':
        case 'net_volume_hk':
          formattedRow[chineseName] = formatVolume(value);
          break;
        case 'amount':
        case 'amount_post':
        case 'market_capital':
        case 'balance':
          formattedRow[chineseName] = formatAmount(value);
          break;
        case 'percent':
        case 'turnoverrate':
        case 'hold_ratio_cn':
        case 'hold_ratio_hk':
          formattedRow[chineseName] = formatPercent(value);
          break;
        case 'chg':
          formattedRow[chineseName] = formatPrice(value);
          break;
        case 'pe':
        case 'pb':
        case 'ps':
        case 'pcf':
          formattedRow[chineseName] = value ? value.toFixed(4) : null;
          break;
        default:
          formattedRow[chineseName] = value;
      }
    });
    
    return formattedRow;
  });

  return {
    股票代码: symbol,
    列名: column.map(col => columnMap[col] || col),
    K线数据: formattedItems,
  };
}

// 格式化 margin 响应
function formatMarginResponse(response) {
  if (!response.data) {
    return response;
  }

  const { data } = response;
  const { items, inclusion_date } = data;

  // 格式化融资融券数据
  const formattedItems = items.map((item) => ({
    交易日期: formatTimestamp(item.td_date),
    融资余额: formatAmount(item.margin_trading_amt_balance),
    融券余额: formatAmount(item.short_selling_amt_balance),
    融资净买入: formatAmount(item.margin_trading_net_buy_amt),
    融资买入额: formatAmount(item.margin_trading_buy_amt),
  }));

  return {
    融资融券数据: formattedItems,
    纳入日期: formatTimestamp(inclusion_date),
  };
}

// 格式化 blocktrans 响应
function formatBlocktransResponse(response) {
  if (!response.data) {
    return response;
  }

  const { data } = response;
  const { items } = data;

  // 格式化大宗交易数据
  const formattedItems = items.map((item) => ({
    交易日期: formatTimestamp(item.td_date),
    成交量: formatVolume(item.vol),
    成交价格: formatPrice(item.trans_price),
    成交金额: formatAmount(item.trans_amt),
    溢价率: formatPercent(item.premium_rat),
    买方营业部: item.buy_branch_org_name,
    卖方营业部: item.sell_branch_org_name,
  }));

  return {
    大宗交易数据: formattedItems,
  };
}

// 格式化 capitalAssort 响应
function formatCapitalAssortResponse(response) {
  if (!response.data) {
    return response;
  }

  const { data } = response;

  return {
    时间: formatTimestamp(data.timestamp),
    买入资金: {
      大单买入: formatAmount(data.buy_large),
      中单买入: formatAmount(data.buy_medium),
      小单买入: formatAmount(data.buy_small),
      买入总计: formatAmount(data.buy_total),
      超大单买入: data.buy_xlarge ? formatAmount(data.buy_xlarge) : null,
    },
    卖出资金: {
      大单卖出: formatAmount(data.sell_large),
      中单卖出: formatAmount(data.sell_medium),
      小单卖出: formatAmount(data.sell_small),
      卖出总计: formatAmount(data.sell_total),
      超大单卖出: data.sell_xlarge ? formatAmount(data.sell_xlarge) : null,
    },

  };
}

// 格式化 capitalFlow 响应
function formatCapitalFlowResponse(response) {
  if (!response.data) {
    return response;
  }

  const { data } = response;
  const { symbol, items } = data;

  // 格式化资金流向数据
  const formattedItems = items.map((item) => ({
    时间: formatTimestamp(item.timestamp),
    资金流向: formatAmount(item.amount),
    类型: item.type,
  }));

  return {
    股票代码: symbol,
    资金流向数据: formattedItems,
  };
}

// 格式化 capitalHistory 响应
function formatCapitalHistoryResponse(response) {
  if (!response.data) {
    return response;
  }

  const { data } = response;
  const { sum3, sum5, sum10, sum20, items } = data;

  // 格式化资金历史数据
  const formattedItems = items.map((item) => ({
    时间: formatTimestamp(item.timestamp),
    资金流向: formatAmount(item.amount),
  }));

  return {
    累计统计: {
      "3日累计": formatAmount(sum3),
      "5日累计": formatAmount(sum5),
      "10日累计": formatAmount(sum10),
      "20日累计": formatAmount(sum20),
    },
    资金历史数据: formattedItems,
  };
}

// 格式化 skholderchg 响应
function formatSkholderchgResponse(response) {
  if (!response.data) {
    return response;
  }

  const { data } = response;
  const { items } = data;

  // 格式化股东变化数据
  const formattedItems = items.map((item) => ({
    管理人员姓名: item.manage_name,
    变动日期: formatTimestamp(item.chg_date),
    交易均价: formatPrice(item.trans_avg_price),
    变动股数: formatShares(item.chg_shares_num),
    变动类型: item.chg_shares_num > 0 ? "增持" : "减持",
  }));

  return {
    股东变化数据: formattedItems,
  };
}

// 格式化 skholder 响应
function formatSkholderResponse(response) {
  if (!response.data) {
    return response;
  }

  const { data } = response;
  const { items } = data;

  // 格式化股东数据
  const formattedItems = items.map((item) => ({
    姓名: item.personal_name,
    职位: item.position_name,
    任职开始日期: formatTimestamp(item.employ_date),
    任职结束日期: item.employ_ed ? formatTimestamp(item.employ_ed) : "至今",
    持股数量: item.held_num ? formatShares(item.held_num) : "无持股",
    年薪: item.annual_salary ? formatAmount(item.annual_salary) : "未披露",
    简历: item.resume_cn,
  }));

  return {
    股东数据: formattedItems,
  };
}

// 格式化 industry 响应
function formatIndustryResponse(response) {
  if (!response.data) {
    return response;
  }

  const { data } = response;
  const { concept, concept_class, industry, company, industry_class } = data;

  // 格式化概念板块数据
  const formattedConcepts = concept.map((item) => ({
    概念名称: item.ind_name,
    概念代码: item.ind_code,
  }));

  // 格式化行业数据
  const formattedIndustries = industry.map((item) => ({
    行业名称: item.ind_name,
    行业代码: item.ind_code,
  }));

  // 格式化公司信息
  const formattedCompany = {
    公司名称: company.org_name_cn,
    分类名称: company.classi_name,
    所在省份: company.provincial_name,
    上市日期: formatTimestamp(company.listed_date),
    主营业务: company.main_operation_business,
    实际控制人: company.actual_controller || "未披露",
  };

  return {
    概念板块: {
      概念列表: formattedConcepts,
      概念分类: concept_class,
      概念数量: concept.length,
    },
    行业分类: {
      行业列表: formattedIndustries,
      行业分类标准: industry_class,
      行业数量: industry.length,
    },
    公司信息: formattedCompany,
  };
}

// 格式化 holders 响应
function formatHoldersResponse(response) {
  if (!response.data) {
    return response;
  }

  const { data } = response;
  const { items } = data;

  // 格式化持有人数据
  const formattedItems = items.map((item) => ({
    统计日期: formatTimestamp(item.timestamp),
    股价: formatPrice(item.price),
    股价涨跌: item.chg ? formatPrice(item.chg) : "无数据",
    股东户数: item.ashare_holder ? item.ashare_holder.toLocaleString() + "户" : "无数据",
    户均持股: item.per_float ? formatShares(item.per_float) : "无数据",
    户均持股变化: item.per_float_chg ? formatShares(item.per_float_chg) : "无数据",
    户均金额: item.per_amount ? formatAmount(item.per_amount) : "无数据",
    前十大股东持股比例: item.top_holder_ratio ? formatPercent(item.top_holder_ratio) : "无数据",
    前十大流通股东持股比例: item.top_float_holder_ratio ? formatPercent(item.top_float_holder_ratio) : "无数据",
  }));



  return {
    持有人数据: formattedItems,
  };
}

// 格式化 bonus 响应
function formatBonusResponse(response) {
  if (!response.data) {
    return response;
  }

  const { data } = response;
  const { addtions, allots, items } = data;

  // 格式化分红数据
  const formattedDividends = items.map((item) => ({
    分红年度: item.dividend_year,
    除权日期: formatTimestamp(item.equity_date),
    除息日期: formatTimestamp(item.ashare_ex_dividend_date),
    分红方案: item.plan_explain,
    取消分红日期: item.cancle_dividend_date ? formatTimestamp(item.cancle_dividend_date) : "无",
  }));

  // 格式化增发数据
  const formattedAdditions = addtions.map((item) => ({
    上市日期: formatTimestamp(item.listing_ad),
    实际发行数量: formatShares(item.actual_issue_vol),
    实际发行价格: formatPrice(item.actual_issue_price),
    实际募集净额: formatAmount(item.actual_rc_net_amt),
  }));

  // 格式化配股数据
  const formattedAllots = allots.map((item) => ({
    配股说明书日期: formatTimestamp(item.allot_specification_ad),
    配股价格: formatPrice(item.allot_price_ps),
    实际配股数量: formatShares(item.actual_allot_num),
    实际募集总额: formatAmount(item.actua_rc_total_amt),
  }));



  return {
    分红数据: {
      分红记录: formattedDividends,
    },
    增发数据: {
      增发记录: formattedAdditions,
    },
    配股数据: {
      配股记录: formattedAllots,
    },
  };
}

// 格式化 orgHoldingChange 响应
function formatOrgHoldingChangeResponse(response) {
  if (!response.data || !Array.isArray(response.data.items)) {
    return response;
  }

  const { items } = response.data;

  // 格式化列表数据
  const formattedItems = items.map((item) => ({
    报告日期: item.chg_date,
    机构数量: item.institution_num + "家",
    持股变动: formatPercent(item.chg),
    持股比例: formatPercent(item.held_ratio),
    股价: formatPrice(item.price),
    时间戳: formatTimestamp(item.timestamp),
  }));



  return {
    机构持股变动数据: formattedItems,
  };
}

// 格式化 industryCompare 响应
function formatIndustryCompareResponse(response) {
  if (!response.data) {
    return response;
  }

  const { data } = response;

  // 格式化行业基本信息
  const industryInfo = {
    行业名称: data.ind_name,
    行业代码: data.ind_code,
    行业分类: data.ind_class,
    报告日期: formatTimestamp(data.quote_time),
    报告名称: data.report_name,
    统计公司数: data.count + "家",
  };

  // 格式化平均值数据
  const avgData = {
    市盈率TTM: data.avg.pe_ttm ? data.avg.pe_ttm.toFixed(2) : null,
    基本每股收益: data.avg.basic_eps ? data.avg.basic_eps.toFixed(4) + "元" : null,
    平均净资产收益率: data.avg.avg_roe ? formatPercent(data.avg.avg_roe) : null,
    毛利率: data.avg.gross_selling_rate ? formatPercent(data.avg.gross_selling_rate) : null,
    营业收入: data.avg.total_revenue ? formatAmount(data.avg.total_revenue) : null,
    净利润: data.avg.net_profit_atsopc ? formatAmount(data.avg.net_profit_atsopc) : null,
    每股净资产: data.avg.np_per_share ? data.avg.np_per_share.toFixed(2) + "元" : null,
    每股经营现金流: data.avg.operate_cash_flow_ps ? data.avg.operate_cash_flow_ps.toFixed(2) + "元" : null,
    总资产: data.avg.total_assets ? formatAmount(data.avg.total_assets) : null,
    总股本: data.avg.total_shares ? formatShares(data.avg.total_shares) : null,
    总市值: data.avg.market_capital ? formatMarketCap(data.avg.market_capital) : null,
    营业收入同比增长: data.avg.operating_income_yoy ? formatPercent(data.avg.operating_income_yoy) : null,
    净利润同比增长: data.avg.net_profit_atsopc_yoy ? formatPercent(data.avg.net_profit_atsopc_yoy) : null,
    市净率: data.avg.pb ? data.avg.pb.toFixed(3) : null,
    资产负债率: data.avg.asset_liab_ratio ? formatPercent(data.avg.asset_liab_ratio) : null,
    净利率: data.avg.net_selling_rate ? formatPercent(data.avg.net_selling_rate) : null,
  };

  // 格式化最小值数据
  const minData = {
    市盈率TTM: data.min.pe_ttm ? data.min.pe_ttm.toFixed(2) : null,
    基本每股收益: data.min.basic_eps ? data.min.basic_eps.toFixed(4) + "元" : null,
    平均净资产收益率: data.min.avg_roe ? formatPercent(data.min.avg_roe) : null,
    毛利率: data.min.gross_selling_rate ? formatPercent(data.min.gross_selling_rate) : null,
    营业收入: data.min.total_revenue ? formatAmount(data.min.total_revenue) : null,
    净利润: data.min.net_profit_atsopc ? formatAmount(data.min.net_profit_atsopc) : null,
    每股净资产: data.min.np_per_share ? data.min.np_per_share.toFixed(2) + "元" : null,
    每股经营现金流: data.min.operate_cash_flow_ps ? data.min.operate_cash_flow_ps.toFixed(2) + "元" : null,
    总资产: data.min.total_assets ? formatAmount(data.min.total_assets) : null,
    总股本: data.min.total_shares ? formatShares(data.min.total_shares) : null,
  };

  // 格式化最大值数据
  const maxData = {
    市盈率TTM: data.max.pe_ttm ? data.max.pe_ttm.toFixed(2) : null,
    基本每股收益: data.max.basic_eps ? data.max.basic_eps.toFixed(4) + "元" : null,
    平均净资产收益率: data.max.avg_roe ? formatPercent(data.max.avg_roe) : null,
    毛利率: data.max.gross_selling_rate ? formatPercent(data.max.gross_selling_rate) : null,
    营业收入: data.max.total_revenue ? formatAmount(data.max.total_revenue) : null,
    净利润: data.max.net_profit_atsopc ? formatAmount(data.max.net_profit_atsopc) : null,
    每股净资产: data.max.np_per_share ? data.max.np_per_share.toFixed(2) + "元" : null,
    每股经营现金流: data.max.operate_cash_flow_ps ? data.max.operate_cash_flow_ps.toFixed(2) + "元" : null,
    总资产: data.max.total_assets ? formatAmount(data.max.total_assets) : null,
    总股本: data.max.total_shares ? formatShares(data.max.total_shares) : null,
  };

  // 格式化个股数据
  const formattedItems = data.items.map((item) => ({
    股票代码: item.symbol,
    股票名称: item.name,
    基本每股收益: item.basic_eps ? item.basic_eps.toFixed(4) + "元" : null,
    营业收入: item.total_revenue ? formatAmount(item.total_revenue) : null,
    毛利率: item.gross_selling_rate ? formatPercent(item.gross_selling_rate) : null,
    净利润: item.net_profit_atsopc ? formatAmount(item.net_profit_atsopc) : null,
    每股净资产: item.np_per_share ? item.np_per_share.toFixed(2) + "元" : null,
    平均净资产收益率: item.avg_roe ? formatPercent(item.avg_roe) : null,
    市盈率TTM: item.pe_ttm ? item.pe_ttm.toFixed(2) : null,
    总资产: item.total_assets ? formatAmount(item.total_assets) : null,
    每股经营现金流: item.operate_cash_flow_ps ? item.operate_cash_flow_ps.toFixed(2) + "元" : null,
    总股本: item.total_shares ? formatShares(item.total_shares) : null,
    总市值: item.market_capital ? formatMarketCap(item.market_capital) : null,
    营业收入同比增长: item.operating_income_yoy ? formatPercent(item.operating_income_yoy) : null,
    净利润同比增长: item.net_profit_atsopc_yoy ? formatPercent(item.net_profit_atsopc_yoy) : null,
    市净率: item.pb ? item.pb.toFixed(3) : null,
    资产负债率: item.asset_liab_ratio ? formatPercent(item.asset_liab_ratio) : null,
    净利率: item.net_selling_rate ? formatPercent(item.net_selling_rate) : null,
  }));

  return {
    行业信息: industryInfo,
    行业平均值: avgData,
    行业最小值: minData,
    行业最大值: maxData,
    个股数据: formattedItems,
  };
}

// 格式化 businessAnalysis 响应
function formatBusinessAnalysisResponse(response) {
  if (!response.data || !response.data.items || !Array.isArray(response.data.items)) {
    return response;
  }

  const formattedItems = response.data.items.map((item) => ({
    报告日期: item.report_date || "未知",
    经营分析说明: item.operating_analysis_explain || "暂无数据",
  }));

  return {
    经营分析报告: formattedItems,
  };
}

// 格式化分类列表
function formatClassList(classList) {
  if (!Array.isArray(classList)) {
    return classList;
  }

  return classList.map((classItem) => ({
    分类标准: getClassStandardName(classItem.class_standard),
    业务列表: formatBusinessList(classItem.business_list),
  }));
}

// 格式化业务列表
function formatBusinessList(businessList) {
  if (!Array.isArray(businessList)) {
    return businessList;
  }

  return businessList.map((business) => ({
    项目名称: business.project_announced_name,
    主营业务收入: formatAmount(business.prime_operating_income),
    收入占比: formatRatio(business.income_ratio),
    毛利率: business.gross_profit_rate ? formatRatio(business.gross_profit_rate) : null,
  }));
}

// 获取分类标准名称
function getClassStandardName(standard) {
  const standardMap = {
    1: "按业务类型分类",
    2: "按收入来源分类",
    3: "按地区分类",
  };
  return standardMap[standard] || `分类标准${standard}`;
}

// 格式化现金流量项目
function formatCashFlowItem(item) {
  if (!Array.isArray(item) || item.length !== 2) {
    return item;
  }

  const [amount, ratio] = item;
  return {
    金额: formatAmount(amount),
    同比增长: formatRatio(ratio),
  };
}

// 格式化财务指标项目
function formatIndicatorItem(item, unit) {
  if (!Array.isArray(item) || item.length !== 2) {
    return item;
  }

  const [value, ratio] = item;
  return {
    数值: formatValue(value, unit),
    同比增长: formatRatio(ratio),
  };
}

// 格式化资产负债表项目
function formatBalanceItem(item, unit) {
  if (!Array.isArray(item) || item.length !== 2) {
    return item;
  }

  const [value, ratio] = item;
  return {
    数值: formatValue(value, unit),
    同比增长: formatRatio(ratio),
  };
}

// 格式化利润表项目
function formatIncomeItem(item, unit) {
  if (!Array.isArray(item) || item.length !== 2) {
    return item;
  }

  const [value, ratio] = item;
  return {
    数值: formatValue(value, unit),
    同比增长: formatRatio(ratio),
  };
}

// 格式化价格
function formatPrice(price) {
  if (price === null || price === undefined) {
    return null;
  }
  return price.toFixed(2) + "元";
}

// 格式化百分比
function formatPercent(percent) {
  if (percent === null || percent === undefined) {
    return null;
  }
  // 确保是数字类型
  const num = parseFloat(percent);
  if (isNaN(num)) {
    return null;
  }
  return num.toFixed(2) + "%";
}

// 格式化成交量
function formatVolume(volume) {
  if (volume === null || volume === undefined) {
    return null;
  }
  
  if (volume >= 100000000) {
    return (volume / 100000000).toFixed(2) + "亿手";
  } else if (volume >= 10000) {
    return (volume / 10000).toFixed(2) + "万手";
  } else {
    return volume.toLocaleString() + "手";
  }
}

// 格式化市值
function formatMarketCap(marketCap) {
  if (marketCap === null || marketCap === undefined) {
    return null;
  }
  
  if (marketCap >= 1000000000000) {
    return (marketCap / 1000000000000).toFixed(2) + "万亿元";
  } else if (marketCap >= 100000000) {
    return (marketCap / 100000000).toFixed(2) + "亿元";
  } else if (marketCap >= 10000) {
    return (marketCap / 10000).toFixed(2) + "万元";
  } else {
    return marketCap.toLocaleString() + "元";
  }
}

// 格式化金额
function formatAmount(amount) {
  if (amount === null || amount === undefined) {
    return null;
  }

  if (Math.abs(amount) >= 100000000) {
    return (amount / 100000000).toFixed(2) + "亿元";
  } else if (Math.abs(amount) >= 10000) {
    return (amount / 10000).toFixed(2) + "万元";
  } else {
    return amount.toLocaleString();
  }
}

// 格式化数值
function formatValue(value, unit) {
  if (value === null || value === undefined) {
    return null;
  }

  if (unit === "%") {
    return value.toFixed(2) + "%";
  } else if (unit === "元") {
    return value.toFixed(2) + "元";
  } else if (unit === "亿元") {
    return (value / 100000000).toFixed(2) + "亿元";
  } else {
    return value.toFixed(4);
  }
}

// 格式化比率
function formatRatio(ratio) {
  if (ratio === null || ratio === undefined) {
    return null;
  }

  return (ratio * 100).toFixed(2) + "%";
}

// 格式化时间戳
function formatTimestamp(timestamp) {
  if (!timestamp) {
    return null;
  }

  const date = new Date(timestamp);
  return date.toLocaleDateString("zh-CN") + " " + date.toLocaleTimeString("zh-CN");
}

// 获取机构类型名称
function getOrgTypeName(orgType) {
  const orgTypeMap = {
    1: "个人",
    2: "机构",
    3: "基金",
    4: "券商",
    5: "保险",
    6: "银行",
    7: "信托",
    8: "其他",
  };
  return orgTypeMap[orgType] || `类型${orgType}`;
}

// 格式化股本数量
function formatShares(shares) {
  if (shares === null || shares === undefined) {
    return null;
  }
  
  if (shares >= 100000000) {
    return (shares / 100000000).toFixed(2) + "亿股";
  } else if (shares >= 10000) {
    return (shares / 10000).toFixed(2) + "万股";
  } else {
    return shares.toLocaleString() + "股";
  }
}

// 格式化标签
function formatTags(tags) {
  if (!Array.isArray(tags)) {
    return tags;
  }

  return tags.map((tag) => ({
    描述: tag.description,
    值: tag.value,
  }));
}

// 格式化 shareschg 响应
function formatShareschgResponse(response) {
  if (!response.data || !response.data.items || !Array.isArray(response.data.items)) {
    return response;
  }

  const formattedItems = response.data.items.map((item) => ({
    变动日期: item.chg_date ? formatTimestamp(item.chg_date) : "未知",
    流通股本: item.float_shares ? formatShares(item.float_shares) : "暂无数据",
    总股本: item.total_shares ? formatShares(item.total_shares) : "暂无数据",
    变动原因: item.chg_reason || "暂无说明",
  }));



  return {
    股本变化数据: formattedItems,
  };
}

// 格式化 topHolders 响应
function formatTopHoldersResponse(response) {
  if (!response.data || !response.data.items || !Array.isArray(response.data.items)) {
    return response;
  }
  const { times = [], total = {}, items = [], quit = [] } = response.data;
  // 最新报告期
  const latestTime = times[0] || {};
  // 合计
  const totalInfo = {
    持股变动: typeof total.chg === 'number' ? (total.chg > 0 ? '+' : '') + total.chg.toFixed(2) + '%' : '无',
    持股数量: total.held_num ? formatShares(total.held_num) : '无',
    持股占比: total.held_ratio ? total.held_ratio.toFixed(2) + '%' : '无',
  };
  // 股东明细
  const holders = items.map(item => ({
    股东名称: item.holder_name,
    持股数量: item.held_num ? formatShares(item.held_num) : '无',
    持股占比: item.held_ratio ? item.held_ratio.toFixed(2) + '%' : '无',
    持股变动: typeof item.chg === 'number' ? (item.chg > 0 ? '+' : '') + item.chg.toFixed(2) + '%' : '无',
  }));
  // 退出名单
  const quitList = quit.map(item => ({
    股东名称: item.holder_name,
    退出期数: item.quit_time,
    退出持股数量: item.held_num ? formatShares(item.held_num) : '无',
  }));
  return {
    报告期: latestTime.name || '未知',
    前十大股东合计: totalInfo,
    前十大股东明细: holders,
    退出名单: quitList.length > 0 ? quitList : '无',
  };
}

// 格式化 mainIndicator 响应
function formatMainIndicatorResponse(response) {
  if (!response.data || !response.data.items || !Array.isArray(response.data.items)) {
    return response;
  }

  const item = response.data.items[0]; // 主要指标通常只有一条记录
  if (!item) {
    return response;
  }

  return {
    报告日期: item.report_date || "未知",
    货币: item.currency || "CNY",
    估值指标: {
      市盈率TTM: item.pe_ttm ? item.pe_ttm.toFixed(3) : "暂无数据",
      市净率: item.pb ? item.pb.toFixed(3) : "暂无数据",
      股息: item.dividend ? formatPrice(item.dividend) : "暂无数据",
      股息率: item.dividend_yield ? formatPercent(item.dividend_yield) : "暂无数据",
    },
    盈利能力: {
      基本每股收益: item.basic_eps ? formatPrice(item.basic_eps) : "暂无数据",
      净利润: item.net_profit_atsopc ? formatAmount(item.net_profit_atsopc) : "暂无数据",
      净利润同比增长: item.net_profit_atsopc_yoy ? formatPercent(item.net_profit_atsopc_yoy) : "暂无数据",
      营业收入: item.total_revenue ? formatAmount(item.total_revenue) : "暂无数据",
      营业收入同比增长: item.operating_income_yoy ? formatPercent(item.operating_income_yoy) : "暂无数据",
      净销售利润率: item.net_selling_rate ? formatPercent(item.net_selling_rate) : "暂无数据",
      毛利率: item.gross_selling_rate ? formatPercent(item.gross_selling_rate) : "暂无数据",
    },
    资产质量: {
      每股净资产: item.np_per_share ? formatPrice(item.np_per_share) : "暂无数据",
      平均净资产收益率: item.avg_roe ? formatPercent(item.avg_roe) : "暂无数据",
      资产负债率: item.asset_liab_ratio ? formatPercent(item.asset_liab_ratio) : "暂无数据",
      商誉占净资产比例: item.goodwill_in_net_assets ? formatPercent(item.goodwill_in_net_assets) : "暂无数据",
    },
    股本结构: {
      总股本: item.total_shares ? formatShares(item.total_shares) : "暂无数据",
      流通股本: item.float_shares ? formatShares(item.float_shares) : "暂无数据",
      质押比例: item.pledge_ratio ? formatPercent(item.pledge_ratio) : "暂无数据",
    },
    市值信息: {
      总市值: item.market_capital ? formatMarketCap(item.market_capital) : "暂无数据",
      流通市值: item.float_market_capital ? formatMarketCap(item.float_market_capital) : "暂无数据",
    },
  };
}

// 格式化 navDaily 响应
function formatNavDailyResponse(response) {
  if (!Array.isArray(response)) {
    return response;
  }

  const formattedData = response.map((cube) => {
    const { symbol, name, list } = cube;
    
    // 格式化净值数据
    const formattedList = list.map((item) => ({
      日期: item.date,
      净值: item.value.toFixed(4),
      涨跌幅: formatPercent(item.percent),
      时间戳: formatTimestamp(item.time),
    }));



    return {
      组合代码: symbol,
      组合名称: name,
      净值数据: formattedList,
    };
  });

  return {
    组合净值数据: formattedData,
    组合数量: response.length,
  };
}

// 格式化 rebalancingHistory 响应
function formatRebalancingHistoryResponse(response) {
  if (!response.list || !Array.isArray(response.list)) {
    return response;
  }

  const formattedList = response.list.map((item) => {
    // 格式化持仓历史
    const formattedHistories = item.rebalancing_histories?.map((history) => ({
      股票代码: history.stock_symbol,
      股票名称: history.stock_name,
      调整后数量: formatVolume(history.volume),
      调整后价格: formatPrice(history.price),
      调整后净值: formatAmount(history.net_value),
      调整后权重: formatPercent(history.weight),
      目标权重: formatPercent(history.target_weight),
      调整前权重: formatPercent(history.prev_weight),
      调整前目标权重: formatPercent(history.prev_target_weight),
      调整前权重调整: formatPercent(history.prev_weight_adjusted),
      调整前数量: formatVolume(history.prev_volume),
      调整前价格: formatPrice(history.prev_price),
      调整前净值: formatAmount(history.prev_net_value),
      目标数量: formatVolume(history.target_volume),
      调整前目标数量: formatVolume(history.prev_target_volume),
      是否主动调整: history.proactive ? "是" : "否",
      创建时间: formatTimestamp(history.created_at),
      更新时间: formatTimestamp(history.updated_at),
    })) || [];

    return {
      调仓ID: item.id,
      状态: item.status,
      组合ID: item.cube_id,
      前次调仓ID: item.prev_bebalancing_id,
      调仓类别: item.category,
      执行策略: item.exe_strategy,
      创建时间: formatTimestamp(item.created_at),
      更新时间: formatTimestamp(item.updated_at),
      现金: formatAmount(item.cash),
      现金价值: formatAmount(item.cash_value),
      错误代码: item.error_code,
      错误信息: item.error_message,
      错误状态: item.error_status,
      持仓历史: formattedHistories,
      备注: item.comment,
      差异: item.diff,
      新买入数量: item.new_buy_count,
    };
  });



  return {
    调仓历史: formattedList,
    分页信息: {
      当前页: currentPage,
      最大页数: maxPage,
      总记录数: totalCount,
      当前页记录数: formattedList.length,
    },
  };
}

// 格式化 rebalancingCurrent 响应
function formatRebalancingCurrentResponse(response) {
  if (!response.last_rb || !response.last_success_rb) {
    return response;
  }

  // 格式化最新调仓数据
  const formatRebalancingData = (rbData) => {
    if (!rbData) return null;

    // 格式化持仓数据
    const formattedHoldings = rbData.holdings?.map((holding) => ({
      股票ID: holding.stock_id,
      股票代码: holding.stock_symbol,
      股票名称: holding.stock_name,
      权重: formatPercent(holding.weight),
      数量: formatVolume(holding.volume),
      板块名称: holding.segment_name,
      板块ID: holding.segment_id,
      板块颜色: holding.segment_color,
      是否主动调整: holding.proactive ? "是" : "否",
    })) || [];

    // 格式化调仓历史
    const formattedHistories = rbData.rebalancing_histories?.map((history) => ({
      股票代码: history.stock_symbol,
      股票名称: history.stock_name,
      调整后数量: formatVolume(history.volume),
      调整后价格: formatPrice(history.price),
      调整后净值: formatAmount(history.net_value),
      调整后权重: formatPercent(history.weight),
      目标权重: formatPercent(history.target_weight),
      调整前权重: formatPercent(history.prev_weight),
      调整前目标权重: formatPercent(history.prev_target_weight),
      调整前权重调整: formatPercent(history.prev_weight_adjusted),
      调整前数量: formatVolume(history.prev_volume),
      调整前价格: formatPrice(history.prev_price),
      调整前净值: formatAmount(history.prev_net_value),
      目标数量: formatVolume(history.target_volume),
      调整前目标数量: formatVolume(history.prev_target_volume),
      是否主动调整: history.proactive ? "是" : "否",
      创建时间: formatTimestamp(history.created_at),
      更新时间: formatTimestamp(history.updated_at),
    })) || [];

    return {
      调仓ID: rbData.id,
      状态: rbData.status,
      组合ID: rbData.cube_id,
      前次调仓ID: rbData.prev_bebalancing_id,
      调仓类别: rbData.category,
      执行策略: rbData.exe_strategy,
      创建时间: formatTimestamp(rbData.created_at),
      更新时间: formatTimestamp(rbData.updated_at),
      现金: formatAmount(rbData.cash),
      现金价值: formatAmount(rbData.cash_value),
      错误代码: rbData.error_code,
      错误信息: rbData.error_message,
      错误状态: rbData.error_status,
      持仓数据: formattedHoldings,
      调仓历史: formattedHistories,
      备注: rbData.comment,
      差异: rbData.diff,
      新买入数量: rbData.new_buy_count,
    };
  };

  const lastRb = formatRebalancingData(response.last_rb);
  const lastSuccessRb = formatRebalancingData(response.last_success_rb);

  return {
    最新调仓: lastRb,
    最近成功调仓: lastSuccessRb,
  };
}

// 格式化 quoteCurrent 响应
function formatQuoteCurrentResponse(response) {
  if (!response || typeof response !== 'object') {
    return response;
  }

  // 获取第一个组合的数据（通常只有一个组合）
  const symbol = Object.keys(response)[0];
  const data = response[symbol];

  if (!data) {
    return response;
  }

  return {
    组合代码: data.symbol,
    市场: data.market === 'cn' ? '中国' : data.market,
    组合名称: data.name,
    净值: data.net_value ? parseFloat(data.net_value).toFixed(4) : "0.0000",
    日涨幅: data.daily_gain ? formatPercent(parseFloat(data.daily_gain)) : "0.00%",
    月涨幅: data.monthly_gain ? formatPercent(parseFloat(data.monthly_gain)) : "0.00%",
    总涨幅: data.total_gain ? formatPercent(parseFloat(data.total_gain)) : "0.00%",
    年化收益率: data.annualized_gain ? formatPercent(parseFloat(data.annualized_gain)) : "0.00%",
    是否存在: data.hasexist === "true" ? "是" : "否",
    关闭时间: data.closed_at || "未关闭",
    是否有徽章: data.badges_exist === "true" ? "是" : "否",
    游戏ID: data.game_id || 0,
  };
}

// 格式化 fundDetail 响应
function formatFundDetailResponse(response) {
  if (!response.data) {
    return response;
  }

  const { data } = response;
  const { fund_company, fund_position, fund_rates, manager_list, fund_date_conf } = data;

  // 格式化基金公司信息
  const companyInfo = {
    公司介绍: fund_company || "暂无介绍",
  };

  // 格式化基金持仓信息
  const positionInfo = fund_position ? {
    数据来源: fund_position.source_mark || "未知",
    报告日期: formatTimestamp(fund_position.enddate),
    总资产: formatAmount(fund_position.asset_tot),
    资产净值: formatAmount(fund_position.asset_val),
    资产配置: {
      股票占比: formatPercent(fund_position.stock_percent),
      现金占比: formatPercent(fund_position.cash_percent),
      债券占比: formatPercent(fund_position.bond_percent),
      其他占比: formatPercent(fund_position.other_percent),
    },
    股票持仓: fund_position.stock_list?.map(stock => ({
      股票名称: stock.name,
      股票代码: stock.code,
      持仓占比: formatPercent(stock.percent),
      当前价格: formatPrice(stock.current_price),
      涨跌幅: formatPercent(stock.change_percentage),
      行业标签: stock.industry_label,
      相对上期变化: stock.change_of_pre_quarter,
      变化类型: getChangeTypeName(stock.change_of_pre_quarter_type),
    })) || [],
    债券持仓: fund_position.bond_list?.map(bond => ({
      债券名称: bond.name,
      债券代码: bond.code,
      持仓占比: formatPercent(bond.percent),
    })) || [],
    行业分布: fund_position.industry_list?.map(industry => ({
      行业名称: industry.industry_name,
      行业代码: industry.industry_code,
      持仓占比: formatPercent(industry.percent),
      颜色: industry.color,
    })) || [],
  } : null;

  // 格式化基金费率信息
  const ratesInfo = fund_rates ? {
    基金代码: fund_rates.fd_code,
    申购费率: formatPercent(fund_rates.subscribe_rate),
    赎回费率: formatPercent(fund_rates.withdraw_rate),
    折扣: formatPercent(fund_rates.discount),
    申购费率表: fund_rates.subscribe_rate_table?.map(item => ({
      条件: item.name,
      费率: formatPercent(item.value),
      单位: getUnitName(item.unit),
      是否特殊: item.special ? "是" : "否",
    })) || [],
    赎回费率表: fund_rates.withdraw_rate_table?.map(item => ({
      条件: item.name,
      费率: formatPercent(item.value),
      单位: getUnitName(item.unit),
      是否特殊: item.special ? "是" : "否",
    })) || [],
    其他费率表: fund_rates.other_rate_table?.map(item => ({
      项目: item.name,
      费率: formatPercent(item.value),
      单位: getUnitName(item.unit),
      是否特殊: item.special ? "是" : "否",
    })) || [],
  } : null;

  // 格式化基金经理信息
  const managerInfo = manager_list?.map(manager => ({
    经理ID: manager.indi_id,
    姓名: manager.name,
    简历: manager.resume || "暂无简历",
    毕业院校: manager.college || "未披露",
    工作年限: manager.work_year ? `${manager.work_year}年` : "未披露",
    业绩列表: manager.achievement_list?.map(achievement => ({
      基金代码: achievement.fund_code,
      基金名称: achievement.fundsname,
      任职日期: achievement.post_date,
      离任日期: achievement.resi_date || "至今",
      任职期间收益率: formatPercent(achievement.cp_rate),
    })) || [],
  })) || [];

  // 格式化基金日期配置
  const dateConfInfo = fund_date_conf ? {
    基金代码: fund_date_conf.fd_code,
    申购确认日期: `${fund_date_conf.buy_confirm_date}天`,
    申购查询日期: `${fund_date_conf.buy_query_date}天`,
    赎回确认日期: `${fund_date_conf.sale_confirm_date}天`,
    赎回查询日期: `${fund_date_conf.sale_query_date}天`,
    总申购天数: `${fund_date_conf.all_buy_days}天`,
    总赎回天数: `${fund_date_conf.all_sale_days}天`,
  } : null;

  return {
    基金公司信息: companyInfo,
    基金持仓信息: positionInfo,
    基金费率信息: ratesInfo,
    基金经理信息: managerInfo,
    基金日期配置: dateConfInfo,
    是否养老金基金: data.pension_fund ? "是" : "否",
  };
}

// 格式化 fundInfo 响应
function formatFundInfoResponse(response) {
  if (!response.data) {
    return response;
  }

  const { data } = response;
  const { fund_derived, fund_rates, op_fund, trade_reason, fir_header_base_data, sec_header_base_data, nav_tab_list, benchmark_index } = data;

  // 格式化基本信息
  const basicInfo = {
    基金代码: data.fd_code,
    基金类型: getFundTypeName(data.fd_type),
    基金名称: data.fd_name,
    基金全称: data.fd_full_name,
    成立日期: formatTimestamp(data.found_date),
    基金状态: getFundStatusName(data.fd_status),
    申购状态: getSubscribeStatusName(data.subscribe_status),
    赎回状态: getWithdrawStatusName(data.withdraw_status),
    自动投资状态: getAutoInvestStatusName(data.auto_invest_status),
    基金规模: data.totshare,
    托管人: data.keeper_name,
    基金经理: data.manager_name,
    销售机构: data.trup_name,
    销售状态: getSaleStatusName(data.sale_status),
    风险等级: getRiskLevelName(data.risk_level),
    募集开始日期: formatTimestamp(data.ipo_start_date),
    募集结束日期: formatTimestamp(data.ipo_end_date),
    基金状态: getFundStatusName(data.fund_status),
    是否养老金基金: data.pension_fund ? "是" : "否",
    是否可购买: data.can_buy ? "是" : "否",
    是否可预约购买: data.can_pre_buy ? "是" : "否",
  };

  // 格式化基金衍生数据
  const derivedInfo = fund_derived ? {
    最新净值日期: fund_derived.end_date,
    单位净值: fund_derived.unit_nav,
    日涨跌幅: formatPercent(fund_derived.nav_grtd),
    近1月收益率: formatPercent(fund_derived.nav_grl1m),
    近3月收益率: formatPercent(fund_derived.nav_grl3m),
    近6月收益率: formatPercent(fund_derived.nav_grl6m),
    今年以来收益率: formatPercent(fund_derived.nav_grlty),
    近1年收益率: formatPercent(fund_derived.nav_grl1y),
    近3年收益率: formatPercent(fund_derived.nav_grl3y),
    近5年收益率: formatPercent(fund_derived.nav_grl5y),
    成立以来收益率: formatPercent(fund_derived.nav_grbase),
    近1月排名: fund_derived.srank_l1m,
    近3月排名: fund_derived.srank_l3m,
    近6月排名: fund_derived.srank_l6m,
    今年以来排名: fund_derived.srank_lty,
    近1年排名: fund_derived.srank_l1y,
    近3年排名: fund_derived.srank_l3y,
    近5年排名: fund_derived.srank_l5y,
    净值增长率: formatPercent(fund_derived.nav_growth),
    年度业绩列表: fund_derived.annual_performance_list?.map(item => ({
      期间: item.period,
      收益率: formatPercent(item.nav),
      排名: item.rank,
    })) || [],
    收益率历史: fund_derived.yield_history?.map(item => ({
      期间: item.name,
      收益率: formatPercent(item.yield),
    })) || [],
  } : null;

  // 格式化基金费率
  const ratesInfo = fund_rates ? {
    申购费率: formatPercent(fund_rates.subscribe_rate),
    认购费率: formatPercent(fund_rates.declare_rate),
    折扣: formatPercent(fund_rates.discount),
    申购折扣: formatPercent(fund_rates.subscribe_discount),
    认购折扣: formatPercent(fund_rates.declare_discount),
  } : null;

  // 格式化基金标签
  const fundTags = op_fund?.fund_tags?.map(tag => ({
    分类: getTagCategoryName(tag.category),
    名称: tag.name,
  })) || [];

  // 格式化交易信息
  const tradeInfo = {
    当前收益率: formatPercent(data.yield),
    收益率名称: data.yield_name,
    排名: data.s_rank,
    排名名称: data.s_rank_name,
    增长天数: data.growth_day,
    销售信息: data.sales,
    提示: data.tips,
    类型描述: data.type_desc,
    评级描述: data.rating_desc,
    关注人数: data.follower_count,
    状态数量: data.status_count,
    股票持仓名称: data.stock_position_names,
    是否代理销售: data.agent_sell ? "是" : "否",
    交易原因: {
      显示按钮: trade_reason?.show_button ? "是" : "否",
      赎回显示: trade_reason?.withdraw_display ? "是" : "否",
    },
  };

  // 格式化主要数据
  const mainData = {
    主要数据: fir_header_base_data?.map(item => ({
      数据名称: item.data_name,
      数据值: item.data_value_str,
      数值: item.data_value_number,
      是否有颜色: item.data_have_colour ? "是" : "否",
    })) || [],
    次要数据: sec_header_base_data?.map(item => ({
      数据名称: item.data_name,
      数据值: item.data_value_str,
      数值: item.data_value_number,
      是否有颜色: item.data_have_colour ? "是" : "否",
      扩展信息: item.data_extend,
    })) || [],
  };

  // 格式化净值标签列表
  const navTabs = nav_tab_list?.map(tab => ({
    标签名称: tab.nav_tab_name,
    标签值: tab.nav_tab_value,
    净值增长率: formatPercent(tab.nav_growth),
    扩展信息: tab.nav_tab_extent,
  })) || [];

  // 格式化基准指数
  const benchmarks = benchmark_index?.map(index => ({
    指数代码: index.symbol,
    指数名称: index.symbol_name,
  })) || [];

  return {
    基本信息: basicInfo,
    基金衍生数据: derivedInfo,
    基金费率: ratesInfo,
    基金标签: fundTags,
    交易信息: tradeInfo,
    主要数据: mainData,
    净值标签列表: navTabs,
    基准指数: benchmarks,
    投资方向: data.invest_orientation,
    投资目标: data.invest_target,
    业绩比较基准: data.performance_bench_mark,
    记录版本: data.record_version,
    锁定基金类型: getLockFundTypeName(data.lock_fund_type),
  };
}

// 获取变化类型名称
function getChangeTypeName(type) {
  const typeMap = {
    1: "增持",
    2: "减持", 
    3: "持平",
    4: "新增",
  };
  return typeMap[type] || `类型${type}`;
}

// 获取单位名称
function getUnitName(unit) {
  const unitMap = {
    1: "元",
    2: "%",
  };
  return unitMap[unit] || `单位${unit}`;
}

// 获取基金类型名称
function getFundTypeName(type) {
  const typeMap = {
    1: "股票型",
    2: "债券型",
    3: "混合型",
    4: "货币型",
    5: "指数型",
    6: "QDII",
    7: "FOF",
    8: "其他",
  };
  return typeMap[type] || `类型${type}`;
}

// 获取基金状态名称
function getFundStatusName(status) {
  const statusMap = {
    0: "正常",
    1: "暂停",
    2: "终止",
  };
  return statusMap[status] || `状态${status}`;
}

// 获取申购状态名称
function getSubscribeStatusName(status) {
  const statusMap = {
    0: "暂停申购",
    1: "开放申购",
  };
  return statusMap[status] || `状态${status}`;
}

// 获取赎回状态名称
function getWithdrawStatusName(status) {
  const statusMap = {
    0: "暂停赎回",
    1: "开放赎回",
  };
  return statusMap[status] || `状态${status}`;
}

// 获取自动投资状态名称
function getAutoInvestStatusName(status) {
  const statusMap = {
    0: "暂停",
    1: "开放",
  };
  return statusMap[status] || `状态${status}`;
}

// 获取销售状态名称
function getSaleStatusName(status) {
  const statusMap = {
    0: "暂停销售",
    1: "正常销售",
  };
  return statusMap[status] || `状态${status}`;
}

// 获取风险等级名称
function getRiskLevelName(level) {
  const levelMap = {
    1: "低风险",
    2: "中低风险",
    3: "中风险",
    4: "中高风险",
    5: "高风险",
  };
  return levelMap[level] || `风险等级${level}`;
}

// 获取标签分类名称
function getTagCategoryName(category) {
  const categoryMap = {
    1: "基金类型",
    2: "投资风格",
    3: "投资主题",
    4: "投资地区",
    5: "投资行业",
    6: "投资策略",
    7: "基金特色",
    8: "基金评级",
    9: "风险等级",
  };
  return categoryMap[category] || `分类${category}`;
}

// 获取锁定基金类型名称
function getLockFundTypeName(type) {
  const typeMap = {
    0: "非锁定",
    1: "定期开放",
    2: "持有期",
    3: "滚动持有",
  };
  return typeMap[type] || `锁定类型${type}`;
}

// 格式化 fundGrowth 响应
function formatFundGrowthResponse(response) {
  if (!response.data) {
    return response;
  }

  const { data } = response;
  const { fund_nav_growth, growth_lines, tip, performance_remark } = data;

  // 格式化基金净值增长数据
  const formattedNavGrowth = fund_nav_growth?.map((item) => ({
    日期: item.date,
    净值: parseFloat(item.nav).toFixed(4),
    日涨跌幅: formatPercent(parseFloat(item.percentage)),
  })) || [];

  // 格式化增长线条数据
  const formattedGrowthLines = growth_lines?.map((line) => ({
    线条名称: line.line_name,
    线条键值: line.line_key,
    线条颜色: line.line_color,
  })) || [];

  // 格式化提示信息
  const formattedTips = tip?.map((tipItem) => ({
    标题: tipItem.title,
    内容: tipItem.content,
  })) || [];



  return {
    基金净值增长数据: formattedNavGrowth,
    增长线条配置: formattedGrowthLines,
    提示信息: formattedTips,
    业绩说明: performance_remark,
  };
}

// 格式化 fundNavHistory 响应
function formatFundNavHistoryResponse(response) {
  if (!response.data) {
    return response;
  }

  const { data } = response;
  const { items, current_page, size, total_items, total_pages } = data;

  // 格式化基金净值历史数据
  const formattedItems = items.map((item) => ({
    日期: item.date,
    净值: parseFloat(item.nav).toFixed(4),
    日涨跌幅: formatPercent(parseFloat(item.percentage)),
  }));

  return {
    基金净值历史数据: formattedItems,
    分页信息: {
      当前页: current_page,
      页面大小: size,
      总记录数: total_items,
      总页数: total_pages,
    },
  };
}

// 格式化 fundAchievement 响应
function formatFundAchievementResponse(response) {
  if (!response.data) {
    return response;
  }

  const { data } = response;
  const { fund_code, annual_performance_list, stage_performance_list } = data;

  // 格式化年度业绩数据
  const formattedAnnualPerformance = annual_performance_list.map((item) => ({
    期间: item.period_time,
    基金收益率: formatPercent(parseFloat(item.self_nav)),
    基金最大回撤: item.self_max_draw_down ? formatPercent(parseFloat(item.self_max_draw_down)) : null,
    基准收益率: item.standard_index_nav ? formatPercent(parseFloat(item.standard_index_nav)) : null,
    基准最大回撤: item.standard_index_max_draw_down ? formatPercent(parseFloat(item.standard_index_max_draw_down)) : null,
    基金排名: item.self_nav_rank || null,
  }));

  // 格式化阶段业绩数据
  const formattedStagePerformance = stage_performance_list.map((item) => ({
    期间: item.period_time,
    基金收益率: formatPercent(parseFloat(item.self_nav)),
    基金最大回撤: item.self_max_draw_down ? formatPercent(parseFloat(item.self_max_draw_down)) : null,
    基准收益率: item.standard_index_nav ? formatPercent(parseFloat(item.standard_index_nav)) : null,
    基准最大回撤: item.standard_index_max_draw_down ? formatPercent(parseFloat(item.standard_index_max_draw_down)) : null,
    基金排名: item.self_nav_rank || null,
  }));

  return {
    基金代码: fund_code,
    年度业绩: formattedAnnualPerformance,
    阶段业绩: formattedStagePerformance,
  };
}

// 格式化 fundAsset 响应
function formatFundAssetResponse(response) {
  if (!response.data) {
    return response;
  }

  const { data } = response;
  const { source, source_mark, stock_percent, cash_percent, bond_percent, other_percent, stock_list, bond_list, chart_list, industry_list, industry_tip } = data;

  // 格式化股票持仓数据
  const formattedStockList = stock_list?.map((stock) => ({
    股票名称: stock.name,
    股票代码: stock.code,
    持仓占比: formatPercent(stock.percent),
    当前价格: formatPrice(stock.current_price),
    涨跌幅: formatPercent(stock.change_percentage),
    相对上期变化: stock.change_of_pre_quarter,
    变化类型: getChangeTypeName(stock.change_of_pre_quarter_type),
    行业标签: stock.industry_label,
  })) || [];

  // 格式化债券持仓数据
  const formattedBondList = bond_list?.map((bond) => ({
    债券名称: bond.name,
    债券代码: bond.code,
    持仓占比: formatPercent(bond.percent),
  })) || [];

  // 格式化资产配置图表数据
  const formattedChartList = chart_list?.map((chart) => ({
    类型描述: chart.type_desc,
    类型: chart.type,
    占比: formatPercent(chart.percent),
    颜色: chart.color,
  })) || [];

  // 格式化行业分布数据
  const formattedIndustryList = industry_list?.map((industry) => ({
    行业代码: industry.industry_code,
    行业名称: industry.industry_name,
    持仓占比: formatPercent(industry.percent),
    颜色: industry.color,
  })) || [];

  // 格式化行业提示信息
  const formattedIndustryTips = industry_tip?.map((tip) => ({
    标题: tip.title,
    内容: tip.content,
  })) || [];

  return {
    数据来源: source,
    数据来源标记: source_mark,
    资产配置: {
      股票占比: formatPercent(stock_percent),
      现金占比: formatPercent(cash_percent),
      债券占比: formatPercent(bond_percent),
      其他占比: formatPercent(other_percent),
    },
    股票持仓: formattedStockList,
    债券持仓: formattedBondList,
    资产配置图表: formattedChartList,
    行业分布: formattedIndustryList,
    行业提示: formattedIndustryTips,
  };
}

// 格式化 fundManager 响应
function formatFundManagerResponse(response) {
  if (!response.data) {
    return response;
  }

  const { data } = response;
  const { items, current_page, size, total_items, total_pages } = data;

  // 格式化基金经理数据
  const formattedItems = items.map((item) => ({
    经理ID: item.indi_id,
    姓名: item.name,
    工作年限: item.work_year ? `${item.work_year}年` : "未披露",
    任职日期: formatTimestamp(item.post_date),
    任职状态: getPostStatusName(item.post_status),
    任职期间: item.cp_term,
    任职期间收益率: formatPercent(parseFloat(item.cp_rate)),
    职位名称: getPostNameName(item.post_name),
    基金总净值: formatAmount(item.fund_total_nav),
  }));

  return {
    基金经理列表: formattedItems,
    分页信息: {
      当前页: current_page,
      页面大小: size,
      总记录数: total_items,
      总页数: total_pages,
    },
  };
}

// 获取任职状态名称
function getPostStatusName(status) {
  const statusMap = {
    0: "离任",
    1: "在职",
  };
  return statusMap[status] || `状态${status}`;
}

// 获取职位名称
function getPostNameName(name) {
  const nameMap = {
    1: "基金经理",
    2: "基金经理助理",
    3: "投资经理",
    4: "投资总监",
    5: "研究总监",
  };
  return nameMap[name] || `职位${name}`;
}

// 格式化 fundTradeDate 响应
function formatFundTradeDateResponse(response) {
  if (!response.data) {
    return response;
  }

  const { data } = response;
  const { 
    buy_query_date, 
    buy_confirm_date, 
    sale_confirm_date, 
    sale_query_date, 
    sale_to_cash_query_date, 
    withdraw_date, 
    if_can_pay_xjb, 
    if_can_sale_xjb, 
    if_can_transition, 
    if_can_sale_to_cash 
  } = data;

  return {
    交易日期信息: {
      申购查询日期: buy_query_date,
      申购确认日期: buy_confirm_date,
      赎回确认日期: sale_confirm_date,
      赎回查询日期: sale_query_date,
      赎回到现金查询日期: sale_to_cash_query_date,
      提现日期: withdraw_date === 0 ? "不支持提现" : withdraw_date,
    },
    交易权限: {
      是否可支付雪球宝: if_can_pay_xjb ? "是" : "否",
      是否可赎回雪球宝: if_can_sale_xjb ? "是" : "否",
      是否可转换: if_can_transition ? "是" : "否",
      是否可赎回到现金: if_can_sale_to_cash ? "是" : "否",
    },
  };
}

// 格式化 fundDerived 响应
function formatFundDerivedResponse(response) {
  if (!response.data) {
    return response;
  }

  const { data } = response;
  const { 
    fd_code, 
    end_date, 
    unit_nav, 
    unit_acc_nav, 
    nav_grtd, 
    nav_grl1w, 
    nav_grl1m, 
    nav_grl3m, 
    nav_grl6m, 
    nav_grlty, 
    nav_grl1y, 
    nav_grl2y, 
    nav_grl3y, 
    nav_grl5y, 
    nav_grbase, 
    srank_l1m, 
    srank_l3m, 
    srank_l6m, 
    srank_lty, 
    srank_l1y, 
    srank_l3y, 
    srank_l5y, 
    srank_base, 
    appl_shares3m, 
    updated_at, 
    nav_growth, 
    fd_type, 
    aip_grl1y, 
    aip_grl2y, 
    aip_grl3y, 
    aip_grl5y, 
    itg_aip_grl1y, 
    itg_aip_grl2y, 
    itg_aip_grl3y, 
    itg_aip_grl5y, 
    annual_performance_list, 
    first_nav, 
    yield_history 
  } = data;

  // 格式化年度业绩数据
  const formattedAnnualPerformance = annual_performance_list?.map((item) => ({
    期间: item.period,
    收益率: formatPercent(parseFloat(item.nav)),
    排名: item.rank,
  })) || [];

  // 格式化收益率历史数据
  const formattedYieldHistory = yield_history?.map((item) => ({
    期间: item.name,
    收益率: formatPercent(parseFloat(item.yield)),
  })) || [];

  return {
    基本信息: {
      基金代码: fd_code,
      基金类型: getFundTypeName(fd_type),
      最新净值日期: end_date,
      单位净值: parseFloat(unit_nav).toFixed(4),
      累计净值: parseFloat(unit_acc_nav).toFixed(4),
      净值增长率: formatPercent(parseFloat(nav_growth)),
      是否首次净值: first_nav ? "是" : "否",
      更新时间: formatTimestamp(updated_at),
    },
    收益率表现: {
      日收益率: formatPercent(parseFloat(nav_grtd)),
      近1周收益率: formatPercent(parseFloat(nav_grl1w)),
      近1月收益率: formatPercent(parseFloat(nav_grl1m)),
      近3月收益率: formatPercent(parseFloat(nav_grl3m)),
      近6月收益率: formatPercent(parseFloat(nav_grl6m)),
      今年以来收益率: formatPercent(parseFloat(nav_grlty)),
      近1年收益率: formatPercent(parseFloat(nav_grl1y)),
      近2年收益率: formatPercent(parseFloat(nav_grl2y)),
      近3年收益率: formatPercent(parseFloat(nav_grl3y)),
      近5年收益率: formatPercent(parseFloat(nav_grl5y)),
      成立以来收益率: formatPercent(parseFloat(nav_grbase)),
    },
    排名信息: {
      近1月排名: srank_l1m,
      近3月排名: srank_l3m,
      近6月排名: srank_l6m,
      今年以来排名: srank_lty,
      近1年排名: srank_l1y,
      近3年排名: srank_l3y,
      近5年排名: srank_l5y,
      成立以来排名: srank_base,
    },
    定投收益: {
      近1年定投收益率: formatPercent(parseFloat(aip_grl1y)),
      近2年定投收益率: formatPercent(parseFloat(aip_grl2y)),
      近3年定投收益率: formatPercent(parseFloat(aip_grl3y)),
      近5年定投收益率: formatPercent(parseFloat(aip_grl5y)),
      近1年智能定投收益率: formatPercent(parseFloat(itg_aip_grl1y)),
      近2年智能定投收益率: formatPercent(parseFloat(itg_aip_grl2y)),
      近3年智能定投收益率: formatPercent(parseFloat(itg_aip_grl3y)),
      近5年智能定投收益率: formatPercent(parseFloat(itg_aip_grl5y)),
    },
    其他信息: {
      近3月申购份额: formatShares(parseInt(appl_shares3m)),
    },
    年度业绩: formattedAnnualPerformance,
    收益率历史: formattedYieldHistory,
  };
}

// 格式化 convertibleBond 响应
function formatConvertibleBondResponse(response) {
  if (!response.result || !response.result.data) {
    return response;
  }

  const { result } = response;
  const { pages, data, count } = result;

  // 格式化可转债数据
  const formattedData = data.map((bond) => ({
    基本信息: {
      债券代码: bond.SECURITY_CODE,
      证券代码: bond.SECUCODE,
      债券简称: bond.SECURITY_NAME_ABBR,
      正股代码: bond.CONVERT_STOCK_CODE,
      正股简称: bond.SECURITY_SHORT_NAME,
      交易市场: getTradeMarketName(bond.TRADE_MARKET),
      债券期限: bond.BOND_EXPIRE ? `${bond.BOND_EXPIRE}年` : "未披露",
      信用评级: bond.RATING,
      发行年份: bond.ISSUE_YEAR,
    },
    发行信息: {
      发行规模: bond.ACTUAL_ISSUE_SCALE ? `${bond.ACTUAL_ISSUE_SCALE}亿元` : "未披露",
      发行价格: bond.ISSUE_PRICE ? `${bond.ISSUE_PRICE}元` : "未披露",
      面值: bond.PAR_VALUE ? `${bond.PAR_VALUE}元` : "未披露",
      发行对象: bond.ISSUE_OBJECT,
      发行方式: bond.PARAM_NAME,
      发行类型: bond.ISSUE_TYPE,
    },
    时间信息: {
      起息日: formatDate(bond.VALUE_DATE),
      到期日: formatDate(bond.EXPIRE_DATE),
      停止交易日: formatDate(bond.CEASE_DATE),
      付息日: bond.PAY_INTEREST_DAY,
      上市日期: formatDate(bond.LISTING_DATE),
      摘牌日期: formatDate(bond.DELIST_DATE),
      债券开始日期: formatDate(bond.BOND_START_DATE),
      证券开始日期: formatDate(bond.SECURITY_START_DATE),
      公开发行开始日期: formatDate(bond.PUBLIC_START_DATE),
      公开发行开始时间: formatDate(bond.PUBLIC_START_DATE_HOURS),
    },
    转股信息: {
      初始转股价: bond.INITIAL_TRANSFER_PRICE ? `${bond.INITIAL_TRANSFER_PRICE}元` : "未披露",
      当前转股价: bond.TRANSFER_PRICE ? `${bond.TRANSFER_PRICE}元` : "未披露",
      正股价格: bond.CONVERT_STOCK_PRICE ? `${bond.CONVERT_STOCK_PRICE}元` : "未披露",
      转股价值: bond.TRANSFER_VALUE ? `${bond.TRANSFER_VALUE}元` : "未披露",
      转股溢价率: bond.TRANSFER_PREMIUM_RATIO ? formatPercent(bond.TRANSFER_PREMIUM_RATIO) : "未披露",
      转股开始日期: formatDate(bond.TRANSFER_START_DATE),
      转股结束日期: formatDate(bond.TRANSFER_END_DATE),
      是否可转股: bond.IS_CONVERT_STOCK === "是" ? "是" : "否",
    },
    价格信息: {
      当前债券价格: bond.CURRENT_BOND_PRICE === "-" ? "未上市" : bond.CURRENT_BOND_PRICE,
      当前债券价格新: bond.CURRENT_BOND_PRICENEW ? `${bond.CURRENT_BOND_PRICENEW}元` : "未披露",
      赎回触发价: bond.REDEEM_TRIG_PRICE ? `${bond.REDEEM_TRIG_PRICE}元` : "未披露",
      回售触发价: bond.RESALE_TRIG_PRICE ? `${bond.RESALE_TRIG_PRICE}元` : "未披露",
      市净率: bond.PBV_RATIO ? bond.PBV_RATIO.toFixed(2) : "未披露",
    },
    条款信息: {
      利率说明: bond.INTEREST_RATE_EXPLAIN,
      回售条款: bond.RESALE_CLAUSE,
      评级机构: bond.PARTY_NAME,
      是否可赎回: bond.IS_REDEEM === "是" ? "是" : "否",
      是否可回售: bond.IS_SELLBACK === "是" ? "是" : "否",
    },
    申购信息: {
      优先配售比例: bond.FIRST_PER_PREPLACING ? formatPercent(bond.FIRST_PER_PREPLACING * 100) : "未披露",
      网上申购上限: bond.ONLINE_GENERAL_AAU ? `${bond.ONLINE_GENERAL_AAU}万元` : "未披露",
      网上中签率: bond.ONLINE_GENERAL_LWR ? formatPercent(bond.ONLINE_GENERAL_LWR * 100) : "未披露",
      发债代码: bond.CORRECODE,
      发债简称: bond.CORRECODE_NAME_ABBR,
      配债代码: bond.CORRECODEO,
      配债简称: bond.CORRECODE_NAME_ABBRO,
    },
    其他信息: {
      债券组合代码: bond.BOND_COMBINE_CODE,
      备注: bond.REMARK,
      赎回类型: bond.REDEEM_TYPE,
      执行原因: bond.EXECUTE_REASON_HS || bond.EXECUTE_REASON_SH,
      通知日期: formatDate(bond.NOTICE_DATE_HS || bond.NOTICE_DATE_SH),
      执行价格: bond.EXECUTE_PRICE_HS || bond.EXECUTE_PRICE_SH,
      登记日期: formatDate(bond.RECORD_DATE_SH),
      执行开始日期: formatDate(bond.EXECUTE_START_DATEHS || bond.EXECUTE_START_DATESH),
      执行结束日期: formatDate(bond.EXECUTE_END_DATE),
      付息日新: bond.PAYDAYNEW,
      首期收益: bond.FIRST_PROFIT,
      票面利率: bond.COUPON_IR ? formatPercent(bond.COUPON_IR) : "未披露",
      禁售期开始: formatDate(bond.IB_START_DATE),
      禁售期结束: formatDate(bond.IB_END_DATE),
      现金流日期: formatDate(bond.CASHFLOW_DATE),
    },
  }));

  return {
    可转债列表: formattedData,
    分页信息: {
      总页数: pages,
      总记录数: count,
      当前页记录数: formattedData.length,
    },
  };
}

// 获取交易市场名称
function getTradeMarketName(market) {
  const marketMap = {
    "CNSESH": "上海证券交易所",
    "CNSESZ": "深圳证券交易所",
  };
  return marketMap[market] || market;
}

// 格式化日期
function formatDate(dateStr) {
  if (!dateStr || dateStr === "null") {
    return null;
  }
  
  // 如果是时间戳格式，使用现有的 formatTimestamp 函数
  if (typeof dateStr === 'number') {
    return formatTimestamp(dateStr);
  }
  
  // 如果是字符串格式的日期，直接返回
  return dateStr;
}

// 格式化 indexBasicInfo 响应
function formatIndexBasicInfoResponse(response) {
  if (!response.data) {
    return response;
  }

  const { data } = response;
  const { 
    indexFullNameCn, 
    indexShortNameCn, 
    indexFullNameEn, 
    indexShortNameEn, 
    indexCode, 
    ric, 
    bloombergid, 
    basicDate, 
    basicIndex, 
    publishDate, 
    publishChannelCn, 
    publishChannelEn, 
    consNumber, 
    adjFreqCn, 
    adjFreqEn, 
    currencyCn, 
    currencyEn, 
    indexType, 
    indexCnDesc, 
    indexEnDesc, 
    weightingType, 
    weightingTypeEn, 
    ifWeightCapped, 
    ifWeightCappedEn, 
    indexCompliance, 
    ifProtect, 
    protectStartDate, 
    protectEndDate, 
    protectCnDesc 
  } = data;

  return {
    基本信息: {
      指数全称中文: indexFullNameCn,
      指数简称中文: indexShortNameCn,
      指数全称英文: indexFullNameEn,
      指数简称英文: indexShortNameEn,
      指数代码: indexCode,
      RIC代码: ric,
      Bloomberg代码: bloombergid,
      指数类型: getIndexTypeName(indexType),
    },
    基准信息: {
      基准日期: basicDate,
      基准点位: basicIndex,
      发布日期: publishDate,
      发布渠道中文: publishChannelCn,
      发布渠道英文: publishChannelEn,
    },
    成分股信息: {
      成分股数量: consNumber ? `${consNumber}只` : "未披露",
      调整频率中文: adjFreqCn,
      调整频率英文: adjFreqEn,
    },
    货币信息: {
      货币中文: currencyCn,
      货币英文: currencyEn,
    },
    权重信息: {
      权重类型: weightingType || "未披露",
      权重类型英文: weightingTypeEn || "未披露",
      是否设置权重上限: ifWeightCapped ? "是" : "否",
      权重上限说明英文: ifWeightCappedEn || "未披露",
    },
    保护机制: {
      指数合规性: indexCompliance || "无",
      是否有保护机制: ifProtect ? "是" : "否",
      保护开始日期: protectStartDate || "无",
      保护结束日期: protectEndDate || "无",
      保护机制说明: protectCnDesc || "无",
    },
    指数描述: {
      中文描述: indexCnDesc,
      英文描述: indexEnDesc,
    },
  };
}

// 获取指数类型名称
function getIndexTypeName(type) {
  const typeMap = {
    "stock": "股票指数",
    "bond": "债券指数",
    "commodity": "商品指数",
    "currency": "货币指数",
    "real_estate": "房地产指数",
    "alternative": "另类投资指数",
  };
  return typeMap[type] || type;
}

// 格式化 indexDetailsData 响应
function formatIndexDetailsDataResponse(response) {
  if (!response.data) {
    return response;
  }

  const { data } = response;
  const { 
    编制方案, 
    发行人名单, 
    备选名单, 
    指数单张, 
    样本权重, 
    样本列表, 
    指数估值 
  } = data;

  // 格式化文件列表
  const formatFileList = (fileList) => {
    if (!Array.isArray(fileList)) {
      return "暂无文件";
    }
    
    return fileList.map((file) => ({
      文件名: file.fileName,
      文件路径: file.filePath,
      文件类型: getFileTypeName(file.fileType),
    }));
  };

  return {
    编制方案: formatFileList(编制方案),
    发行人名单: formatFileList(发行人名单),
    备选名单: formatFileList(备选名单),
    指数单张: formatFileList(指数单张),
    样本权重: formatFileList(样本权重),
    样本列表: formatFileList(样本列表),
    指数估值: formatFileList(指数估值),
  };
}

// 获取文件类型名称
function getFileTypeName(fileType) {
  const typeMap = {
    "pdf": "PDF文档",
    "xls": "Excel表格",
    "xlsx": "Excel表格",
    "doc": "Word文档",
    "docx": "Word文档",
    "txt": "文本文件",
    "csv": "CSV文件",
    "zip": "压缩文件",
    "rar": "压缩文件",
  };
  return typeMap[fileType] || fileType;
}

// 格式化 indexPerf7 响应
function formatIndexPerfResponse(response) {
  if (!response.data || !Array.isArray(response.data)) {
    return response;
  }

  // 格式化指数表现数据
  const formattedData = response.data.map((item) => ({
    交易日期: formatTradeDate(item.tradeDate),
    指数代码: item.indexCode,
    指数全称中文: item.indexNameCnAll,
    指数简称中文: item.indexNameCn,
    指数全称英文: item.indexNameEnAll,
    指数简称英文: item.indexNameEn,
    开盘价: formatIndexPrice(item.open),
    最高价: formatIndexPrice(item.high),
    最低价: formatIndexPrice(item.low),
    收盘价: formatIndexPrice(item.close),
    涨跌额: formatIndexPrice(item.change),
    涨跌幅: formatPercent(item.changePct),
    成交量: formatIndexVolume(item.tradingVol),
    成交额: formatIndexAmount(item.tradingValue),
    成分股数量: item.consNumber ? `${item.consNumber}只` : "未披露",
    市盈率: item.peg ? item.peg.toFixed(2) : "未披露",
  }));

  return {
    指数7天表现数据: formattedData,
    数据条数: formattedData.length,
  };
}

// 格式化交易日期
function formatTradeDate(tradeDate) {
  if (!tradeDate) {
    return null;
  }
  
  // 格式：20250706 -> 2025-07-06
  const year = tradeDate.substring(0, 4);
  const month = tradeDate.substring(4, 6);
  const day = tradeDate.substring(6, 8);
  
  return `${year}-${month}-${day}`;
}

// 格式化指数价格
function formatIndexPrice(price) {
  if (price === null || price === undefined) {
    return null;
  }
  return price.toFixed(2);
}

// 格式化指数成交量
function formatIndexVolume(volume) {
  if (volume === null || volume === undefined) {
    return null;
  }
  
  if (volume >= 100000000) {
    return (volume / 100000000).toFixed(2) + "亿手";
  } else if (volume >= 10000) {
    return (volume / 10000).toFixed(2) + "万手";
  } else {
    return volume.toLocaleString() + "手";
  }
}

// 格式化指数成交额
function formatIndexAmount(amount) {
  if (amount === null || amount === undefined) {
    return null;
  }
  
  if (amount >= 100000000) {
    return (amount / 100000000).toFixed(2) + "亿元";
  } else if (amount >= 10000) {
    return (amount / 10000).toFixed(2) + "万元";
  } else {
    return amount.toLocaleString() + "元";
  }
}

// 格式化 watchList 响应
function formatWatchListResponse(response) {
  if (!response.data) {
    return response;
  }

  const { data } = response;
  const { cubes, funds, stocks } = data;

  // 格式化组合列表
  const formatCubes = (cubesList) => {
    if (!Array.isArray(cubesList)) {
      return [];
    }
    
    return cubesList.map((cube) => ({
      组合ID: cube.id,
      组合名称: cube.name,
      排序ID: cube.order_id,
      分类: getCategoryName(cube.category),
      是否包含: cube.include ? "是" : "否",
      股票数量: cube.symbol_count ? `${cube.symbol_count}只` : "0只",
      类型: getTypeName(cube.type),
    }));
  };

  // 格式化基金列表
  const formatFunds = (fundsList) => {
    if (!Array.isArray(fundsList)) {
      return [];
    }
    
    return fundsList.map((fund) => ({
      基金ID: fund.id,
      基金名称: fund.name,
      排序ID: fund.order_id,
      分类: getCategoryName(fund.category),
      是否包含: fund.include ? "是" : "否",
      基金数量: fund.symbol_count ? `${fund.symbol_count}只` : "0只",
      类型: getTypeName(fund.type),
    }));
  };

  // 格式化股票列表
  const formatStocks = (stocksList) => {
    if (!Array.isArray(stocksList)) {
      return [];
    }
    
    return stocksList.map((stock) => ({
      股票ID: stock.id,
      股票名称: stock.name,
      排序ID: stock.order_id,
      分类: getCategoryName(stock.category),
      是否包含: stock.include ? "是" : "否",
      股票数量: stock.symbol_count ? `${stock.symbol_count}只` : "0只",
      类型: getTypeName(stock.type),
      创建时间: stock.created_at ? formatTimestamp(stock.created_at) : null,
      更新时间: stock.updated_at ? formatTimestamp(stock.updated_at) : null,
    }));
  };

  return {
    组合列表: formatCubes(cubes),
    基金列表: formatFunds(funds),
    股票列表: formatStocks(stocks),
    错误代码: response.error_code || 0,
    错误描述: response.error_description || "",
  };
}

// 格式化 watchStock 响应
function formatWatchStockResponse(response) {
  if (!response.data) {
    return response;
  }

  const { data } = response;
  const { pid, category, stocks } = data;

  // 格式化股票列表
  const formattedStocks = stocks.map((stock) => ({
    股票代码: stock.symbol,
    股票名称: stock.name,
    股票类型: getStockTypeName(stock.type),
    备注: stock.remark || "无",
    交易所: getExchangeName(stock.exchange),
    市场: getMarketplaceName(stock.marketplace),
    创建时间: formatTimestamp(stock.created),
    关注时间: formatTimestamp(stock.watched),
    分类: getCategoryName(stock.category),
  }));

  return {
    自选股列表信息: {
      组合ID: pid,
      分类: getCategoryName(category),
      股票总数: `${stocks.length}只`,
    },
    股票列表: formattedStocks,
    错误代码: response.error_code || 0,
    错误描述: response.error_description || "",
  };
}

// 获取股票类型名称
function getStockTypeName(type) {
  const typeMap = {
    0: "普通股票",
    3: "指数",
    4: "ETF",
    6: "其他证券",
    11: "A股",
    30: "港股",
  };
  return typeMap[type] || `类型${type}`;
}

// 获取交易所名称
function getExchangeName(exchange) {
  const exchangeMap = {
    "NASDAQ": "纳斯达克",
    "NYSE": "纽约证券交易所",
    "AMEX": "美国证券交易所",
    "ARCA": "NYSE Arca",
    "BATS": "BATS交易所",
    "PINK": "粉单市场",
    "INDEXSP": "标普指数",
    "SH": "上海证券交易所",
    "SZ": "深圳证券交易所",
    "HK": "香港交易所",
    "US": "美国市场",
  };
  return exchangeMap[exchange] || exchange;
}

// 获取市场名称
function getMarketplaceName(marketplace) {
  const marketplaceMap = {
    "US": "美国",
    "CN": "中国",
    "HK": "香港",
  };
  return marketplaceMap[marketplace] || marketplace;
}

// 获取分类名称
function getCategoryName(category) {
  const categoryMap = {
    1: "股票",
    2: "基金",
    3: "组合",
  };
  return categoryMap[category] || `分类${category}`;
}

// 获取类型名称
function getTypeName(type) {
  const typeMap = {
    "-1": "系统默认",
    "1": "用户自定义",
  };
  return typeMap[type.toString()] || `类型${type}`;
}

// 格式化 northboundShareholdingSh 响应
function formatNorthboundShareholdingShResponse(response) {
  if (!Array.isArray(response)) {
    return response;
  }

  // 格式化沪股通持股数据
  const formattedData = response.map((item) => ({
    港股代码: item.code,
    公司名称: item.name,
    持股数量: formatShares(parseInt(item.shareholding)),
    持股比例: item.shareholding_percent,
  }));

  return {
    沪股通持股数据: formattedData,
    数据条数: formattedData.length,
  };
}

// 格式化 northboundShareholdingSz 响应
function formatNorthboundShareholdingSzResponse(response) {
  if (!Array.isArray(response)) {
    return response;
  }

  // 格式化深股通持股数据
  const formattedData = response.map((item) => ({
    港股代码: item.code,
    公司名称: item.name,
    持股数量: formatShares(parseInt(item.shareholding)),
    持股比例: item.shareholding_percent,
  }));

  return {
    深股通持股数据: formattedData,
    数据条数: formattedData.length,
  };
}

// 格式化 tranCode 响应
function formatTranCodeResponse(response) {
  if (typeof response !== 'string') {
    return response;
  }

  // 解析股票代码
  const code = response.trim();
  
  // 判断交易所
  let exchange = "未知";
  let market = "未知";
  
  if (code.startsWith("SH")) {
    exchange = "上海证券交易所";
    market = "中国";
  } else if (code.startsWith("SZ")) {
    exchange = "深圳证券交易所";
    market = "中国";
  } else if (code.startsWith("HK")) {
    exchange = "香港交易所";
    market = "香港";
  } else if (code.startsWith("BJ")) {
    exchange = "北京证券交易所";
    market = "中国";
  }

  return {
    转换后代码: code,
    交易所: exchange,
    市场: market,
    原始代码: response,
  };
}
