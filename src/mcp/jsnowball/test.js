/**
 * jsnowball 完整测试脚本
 * 测试所有API方法
 */

import * as ball from "./index.js";
import { formatResponse } from "./dictionary.js";

// 测试用的股票代码
const TEST_SYMBOL = "SZ000001"; // 平安银行
const TEST_FUND_CODE = "000001"; // 华夏成长混合
const TEST_CUBE_SYMBOL = "ZH123456"; // 测试组合代码
const TEST_INDEX_CODE = "000001"; // 上证指数

async function testSuggestStock() {
  console.log("📊 测试 suggestStock");
  try {
    const result = await ball.suggestStock("中国人寿");
    console.log("✅ suggestStock 返回值为: ", JSON.stringify(result));
    console.log("📊 suggestStock 格式化结果:", JSON.stringify(formatResponse(result, "suggestStock"), null, 2));
    return result;
  } catch (error) {
    console.error("❌ suggestStock 失败:", error.message);
    return null;
  }
}

async function testFinance() {
  console.log("\n💰 测试 Finance 模块");

  // // 测试 cashFlow
  // try {
  //     const result = await ball.cashFlow(TEST_SYMBOL, 0, 5);
  //     console.log('✅ cashFlow 返回值为: ', JSON.stringify(result));
  //     console.log('📊 cashFlow 格式化结果:', JSON.stringify(formatResponse(result, 'cashFlow'), null, 2));
  // } catch (error) {
  //     console.error('❌ cashFlow 失败:', error.message);
  // }

  // // 测试 indicator
  // try {
  //   const result = await ball.indicator(TEST_SYMBOL, 0, false, 5);
  //   console.log("✅ indicator 返回值为: ", JSON.stringify(result));
  //   console.log("📊 indicator 格式化结果:", JSON.stringify(formatResponse(result, 'indicator'), null, 2));
  // } catch (error) {
  //   console.error("❌ indicator 失败:", error.message);
  // }

  // // 测试 balance
  // try {
  //     const result = await ball.balance(TEST_SYMBOL, 0, 5);
  //     console.log('✅ balance 返回值为: ', JSON.stringify(result));
  //     console.log('📊 balance 格式化结果:', JSON.stringify(formatResponse(result, 'balance'), null, 2));
  // } catch (error) {
  //     console.error('❌ balance 失败:', error.message);
  // }

  // // 测试 income
  // try {
  //     const result = await ball.income(TEST_SYMBOL, 0, 5);
  //     console.log('✅ income 返回值为: ', JSON.stringify(result));
  //     console.log('📊 income 格式化结果:', JSON.stringify(formatResponse(result, 'income'), null, 2));
  // } catch (error) {
  //     console.error('❌ income 失败:', error.message);
  // }

  // // 测试 business
  // try {
  //     const result = await ball.business(TEST_SYMBOL, 0, 5);
  //     console.log('✅ business 返回值为: ', JSON.stringify(result));
  //     console.log('📊 business 格式化结果:', JSON.stringify(formatResponse(result, 'business'), null, 2));
  // } catch (error) {
  //     console.error('❌ business 失败:', error.message);
  // }
}

async function testRealtime() {
  console.log("\n📈 测试 Realtime 模块");

  // // 测试 quotec
  // try {
  //     const result = await ball.quotec(TEST_SYMBOL);
  //     console.log('✅ quotec 返回值为: ', JSON.stringify(result));
  //     console.log('📊 quotec 格式化结果:', JSON.stringify(formatResponse(result, 'quotec'), null, 2));
  // } catch (error) {
  //     console.error('❌ quotec 失败:', error.message);
  // }

  // // 测试 quoteDetail
  // try {
  //     const result = await ball.quoteDetail(TEST_SYMBOL);
  //     console.log('✅ quoteDetail 返回值为: ', JSON.stringify(result));
  //     console.log('📊 quoteDetail 格式化结果:', JSON.stringify(formatResponse(result, 'quoteDetail'), null, 2));
  // } catch (error) {
  //     console.error('❌ quoteDetail 失败:', error.message);
  // }

  // // 测试 pankou
  // try {
  //     const result = await ball.pankou(TEST_SYMBOL);
  //     console.log('✅ pankou 返回值为: ', JSON.stringify(result));
  //     console.log('📊 pankou 格式化结果:', JSON.stringify(formatResponse(result, 'pankou'), null, 2));
  // } catch (error) {
  //     console.error('❌ pankou 失败:', error.message);
  // }

  // // 测试 kline
  // try {
  //     const result = await ball.kline(TEST_SYMBOL, 'day', 10);
  //     console.log('✅ kline 返回值为: ', JSON.stringify(result));
  //     console.log('📊 kline 格式化结果:', JSON.stringify(formatResponse(result, 'kline'), null, 2));
  // } catch (error) {
  //     console.error('❌ kline 失败:', error.message);
  // }
}

async function testCapital() {
  console.log("\n💸 测试 Capital 模块");

  // // 测试 margin
  // try {
  //     const result = await ball.margin(TEST_SYMBOL, 1, 10);
  //     console.log('✅ margin 返回值为: ', JSON.stringify(result));
  //     console.log('📊 margin 格式化结果:', JSON.stringify(formatResponse(result, 'margin'), null, 2));
  // } catch (error) {
  //     console.error('❌ margin 失败:', error.message);
  // }

  // // 测试 blocktrans
  // try {
  //     const result = await ball.blocktrans(TEST_SYMBOL, 1, 10);
  //     console.log('✅ blocktrans 返回值为: ', JSON.stringify(result));
  //     console.log('📊 blocktrans 格式化结果:', JSON.stringify(formatResponse(result, 'blocktrans'), null, 2));
  // } catch (error) {
  //     console.error('❌ blocktrans 失败:', error.message);
  // }

  // // 测试 capitalAssort
  // try {
  //     const result = await ball.capitalAssort(TEST_SYMBOL);
  //     console.log('✅ capitalAssort 返回值为: ', JSON.stringify(result));
  //     console.log('📊 capitalAssort 格式化结果:', JSON.stringify(formatResponse(result, 'capitalAssort'), null, 2));
  // } catch (error) {
  //     console.error('❌ capitalAssort 失败:', error.message);
  // }

  // // 测试 capitalFlow
  // try {
  //     const result = await ball.capitalFlow(TEST_SYMBOL);
  //     console.log('✅ capitalFlow 返回值为: ', JSON.stringify(result));
  //     console.log('📊 capitalFlow 格式化结果:', JSON.stringify(formatResponse(result, 'capitalFlow'), null, 2));
  // } catch (error) {
  //     console.error('❌ capitalFlow 失败:', error.message);
  // }

  // // 测试 capitalHistory
  // try {
  //     const result = await ball.capitalHistory(TEST_SYMBOL, 10);
  //     console.log('✅ capitalHistory 返回值为: ', JSON.stringify(result));
  //     console.log('📊 capitalHistory 格式化结果:', JSON.stringify(formatResponse(result, 'capitalHistory'), null, 2));
  // } catch (error) {
  //     console.error('❌ capitalHistory 失败:', error.message);
  // }
}

async function testF10() {
  console.log("\n📋 测试 F10 模块");

  // // 测试 skholderchg
  // try {
  //     const result = await ball.skholderchg(TEST_SYMBOL);
  //     console.log('✅ skholderchg 返回值为: ', JSON.stringify(result));
  //     console.log('📊 skholderchg 格式化结果:', JSON.stringify(formatResponse(result, 'skholderchg'), null, 2));
  // } catch (error) {
  //     console.error('❌ skholderchg 失败:', error.message);
  // }

  // // 测试 skholder
  // try {
  //     const result = await ball.skholder(TEST_SYMBOL);
  //     console.log('✅ skholder 返回值为: ', JSON.stringify(result));
  //     console.log('📊 skholder 格式化结果:', JSON.stringify(formatResponse(result, 'skholder'), null, 2));
  // } catch (error) {
  //     console.error('❌ skholder 失败:', error.message);
  // }

  // // 测试 industry
  // try {
  //     const result = await ball.industry(TEST_SYMBOL);
  //     console.log('✅ industry 返回值为: ', JSON.stringify(result));
  //     console.log('📊 industry 格式化结果:', JSON.stringify(formatResponse(result, 'industry'), null, 2));
  // } catch (error) {
  //     console.error('❌ industry 失败:', error.message);
  // }

  // // 测试 holders
  // try {
  //     const result = await ball.holders(TEST_SYMBOL);
  //     console.log('✅ holders 返回值为: ', JSON.stringify(result));
  //     console.log('📊 holders 格式化结果:', JSON.stringify(formatResponse(result, 'holders'), null, 2));
  // } catch (error) {
  //     console.error('❌ holders 失败:', error.message);
  // }

  // // 测试 bonus
  // try {
  //     const result = await ball.bonus(TEST_SYMBOL, 1, 5);
  //     console.log('✅ bonus 返回值为: ', JSON.stringify(result));
  //     console.log('📊 bonus 格式化结果:', JSON.stringify(formatResponse(result, 'bonus'), null, 2));
  // } catch (error) {
  //     console.error('❌ bonus 失败:', error.message);
  // }

  // // 测试 orgHoldingChange
  // try {
  //     const result = await ball.orgHoldingChange(TEST_SYMBOL);
  //     console.log('✅ orgHoldingChange 返回值为: ', JSON.stringify(result));
  //     console.log('📊 orgHoldingChange 格式化结果:', JSON.stringify(formatResponse(result, 'orgHoldingChange'), null, 2));
  // } catch (error) {
  //     console.error('❌ orgHoldingChange 失败:', error.message);
  // }

  // // 测试 industryCompare
  // try {
  //     const result = await ball.industryCompare(TEST_SYMBOL);
  //     console.log('✅ industryCompare 返回值为: ', JSON.stringify(result));
  //     console.log('📊 industryCompare 格式化结果:', JSON.stringify(formatResponse(result, 'industryCompare'), null, 2));
  // } catch (error) {
  //     console.error('❌ industryCompare 失败:', error.message);
  // }

  // // 测试 businessAnalysis
  // try {
  //     const result = await ball.businessAnalysis(TEST_SYMBOL);
  //     console.log('✅ businessAnalysis 返回值为: ', JSON.stringify(result));
  //     console.log('📊 businessAnalysis 格式化结果:', JSON.stringify(formatResponse(result, 'businessAnalysis'), null, 2));
  // } catch (error) {
  //     console.error('❌ businessAnalysis 失败:', error.message);
  // }

  // // 测试 shareschg
  // try {
  //     const result = await ball.shareschg(TEST_SYMBOL, 5);
  //     console.log('✅ shareschg 返回值为: ', JSON.stringify(result));
  //     console.log('📊 shareschg 格式化结果:', JSON.stringify(formatResponse(result, 'shareschg'), null, 2));
  // } catch (error) {
  //     console.error('❌ shareschg 失败:', error.message);
  // }

  // // 测试 topHolders
  // try {
  //     const result = await ball.topHolders(TEST_SYMBOL, 1);
  //     console.log('✅ topHolders 返回值为: ', JSON.stringify(result));
  //     console.log('📊 topHolders 格式化结果:', JSON.stringify(formatResponse(result, 'topHolders'), null, 2));
  // } catch (error) {
  //     console.error('❌ topHolders 失败:', error.message);
  // }

  // // 测试 mainIndicator
  // try {
  //     const result = await ball.mainIndicator(TEST_SYMBOL);
  //     console.log('✅ mainIndicator 返回值为: ', JSON.stringify(result));
  //     console.log('📊 mainIndicator 格式化结果:', JSON.stringify(formatResponse(result, 'mainIndicator'), null, 2));
  // } catch (error) {
  //     console.error('❌ mainIndicator 失败:', error.message);
  // }
}

async function testReport() {
  console.log("\n📰 测试 Report 模块");

  // // 测试 report
  // try {
  //     const result = await ball.report(TEST_SYMBOL);
  //     console.log('✅ report 返回值为: ', JSON.stringify(result));
  //     console.log('📊 report 格式化结果:', JSON.stringify(formatResponse(result, 'report'), null, 2));
  // } catch (error) {
  //     console.error('❌ report 失败:', error.message);
  // }

  // // 测试 earningforecast
  // try {
  //     const result = await ball.earningforecast(TEST_SYMBOL);
  //     console.log('✅ earningforecast 返回值为: ', JSON.stringify(result));
  //     console.log('📊 earningforecast 格式化结果:', JSON.stringify(formatResponse(result, 'earningforecast'), null, 2));
  // } catch (error) {
  //     console.error('❌ earningforecast 失败:', error.message);
  // }
}

async function testCube() {
  console.log("\n🧊 测试 Cube 模块");

  // // 测试 navDaily
  // try {
  //     const result = await ball.navDaily(TEST_CUBE_SYMBOL);
  //     console.log('✅ navDaily 返回值为: ', JSON.stringify(result));
  //     console.log('📊 navDaily 格式化结果:', JSON.stringify(formatResponse(result, 'navDaily'), null, 2));
  // } catch (error) {
  //     console.error('❌ navDaily 失败:', error.message);
  // }

  // // 测试 rebalancingHistory
  // try {
  //     const result = await ball.rebalancingHistory(TEST_CUBE_SYMBOL, 10, 1);
  //     console.log('✅ rebalancingHistory 返回值为: ', JSON.stringify(result));
  //     console.log('📊 rebalancingHistory 格式化结果:', JSON.stringify(formatResponse(result, 'rebalancingHistory'), null, 2));
  // } catch (error) {
  //     console.error('❌ rebalancingHistory 失败:', error.message);
  // }

  // // 测试 rebalancingCurrent
  // try {
  //     const result = await ball.rebalancingCurrent(TEST_CUBE_SYMBOL);
  //     console.log('✅ rebalancingCurrent 返回值为: ', JSON.stringify(result));
  //     console.log('📊 rebalancingCurrent 格式化结果:', JSON.stringify(formatResponse(result, 'rebalancingCurrent'), null, 2));
  // } catch (error) {
  //     console.error('❌ rebalancingCurrent 失败:', error.message);
  // }

  // // 测试 quoteCurrent
  // try {
  //     const result = await ball.quoteCurrent(TEST_CUBE_SYMBOL);
  //     console.log('✅ quoteCurrent 返回值为: ', JSON.stringify(result));
  //     console.log('📊 quoteCurrent 格式化结果:', JSON.stringify(formatResponse(result, 'quoteCurrent'), null, 2));
  // } catch (error) {
  //     console.error('❌ quoteCurrent 失败:', error.message);
  // }
}

async function testFund() {
  console.log("\n🏦 测试 Fund 模块");

  // // 测试 fundDetail
  // try {
  //     const result = await ball.fundDetail(TEST_FUND_CODE);
  //     console.log('✅ fundDetail 返回值为: ', JSON.stringify(result));
  //     console.log('📊 fundDetail 格式化结果:', JSON.stringify(formatResponse(result, 'fundDetail'), null, 2));
  // } catch (error) {
  //     console.error('❌ fundDetail 失败:', error.message);
  // }

  // // 测试 fundInfo
  // try {
  //     const result = await ball.fundInfo(TEST_FUND_CODE);
  //     console.log('✅ fundInfo 返回值为: ', JSON.stringify(result));
  //     console.log('📊 fundInfo 格式化结果:', JSON.stringify(formatResponse(result, 'fundInfo'), null, 2));
  // } catch (error) {
  //     console.error('❌ fundInfo 失败:', error.message);
  // }

  // // 测试 fundGrowth
  // try {
  //     const result = await ball.fundGrowth(TEST_FUND_CODE, 'ty');
  //     console.log('✅ fundGrowth 返回值为: ', JSON.stringify(result));
  //     console.log('📊 fundGrowth 格式化结果:', JSON.stringify(formatResponse(result, 'fundGrowth'), null, 2));
  // } catch (error) {
  //     console.error('❌ fundGrowth 失败:', error.message);
  // }

  // // 测试 fundNavHistory
  // try {
  //     const result = await ball.fundNavHistory(TEST_FUND_CODE, 1, 5);
  //     console.log('✅ fundNavHistory 返回值为: ', JSON.stringify(result));
  //     console.log('📊 fundNavHistory 格式化结果:', JSON.stringify(formatResponse(result, 'fundNavHistory'), null, 2));
  // } catch (error) {
  //     console.error('❌ fundNavHistory 失败:', error.message);
  // }

  // // 测试 fundAchievement
  // try {
  //     const result = await ball.fundAchievement(TEST_FUND_CODE);
  //     console.log('✅ fundAchievement 返回值为: ', JSON.stringify(result));
  //     console.log('📊 fundAchievement 格式化结果:', JSON.stringify(formatResponse(result, 'fundAchievement'), null, 2));
  // } catch (error) {
  //     console.error('❌ fundAchievement 失败:', error.message);
  // }

  // // 测试 fundAsset
  // try {
  //     const result = await ball.fundAsset(TEST_FUND_CODE);
  //     console.log('✅ fundAsset 返回值为: ', JSON.stringify(result));
  //     console.log('📊 fundAsset 格式化结果:', JSON.stringify(formatResponse(result, 'fundAsset'), null, 2));
  // } catch (error) {
  //     console.error('❌ fundAsset 失败:', error.message);
  // }

  // // 测试 fundManager
  // try {
  //     const result = await ball.fundManager(TEST_FUND_CODE, 1);
  //     console.log('✅ fundManager 返回值为: ', JSON.stringify(result));
  //     console.log('📊 fundManager 格式化结果:', JSON.stringify(formatResponse(result, 'fundManager'), null, 2));
  // } catch (error) {
  //     console.error('❌ fundManager 失败:', error.message);
  // }

  // // 测试 fundTradeDate
  // try {
  //     const result = await ball.fundTradeDate(TEST_FUND_CODE);
  //     console.log('✅ fundTradeDate 返回值为: ', JSON.stringify(result));
  //     console.log('📊 fundTradeDate 格式化结果:', JSON.stringify(formatResponse(result, 'fundTradeDate'), null, 2));
  // } catch (error) {
  //     console.error('❌ fundTradeDate 失败:', error.message);
  // }

  // // 测试 fundDerived
  // try {
  //     const result = await ball.fundDerived(TEST_FUND_CODE);
  //     console.log('✅ fundDerived 返回值为: ', JSON.stringify(result));
  //     console.log('📊 fundDerived 格式化结果:', JSON.stringify(formatResponse(result, 'fundDerived'), null, 2));
  // } catch (error) {
  //     console.error('❌ fundDerived 失败:', error.message);
  // }
}

async function testBond() {
  console.log("\n💎 测试 Bond 模块");

  // // 测试 convertibleBond
  // try {
  //     const result = await ball.convertibleBond(10, 1);
  //     console.log('✅ convertibleBond 返回值为: ', JSON.stringify(result));
  //     console.log('📊 convertibleBond 格式化结果:', JSON.stringify(formatResponse(result, 'convertibleBond'), null, 2));
  // } catch (error) {
  //     console.error('❌ convertibleBond 失败:', error.message);
  // }
}

async function testIndexData() {
  console.log("\n📊 测试 IndexData 模块");

  // // 测试 indexBasicInfo
  // try {
  //     const result = await ball.indexBasicInfo(TEST_INDEX_CODE);
  //     console.log('✅ indexBasicInfo 返回值为: ', JSON.stringify(result));
  //     console.log('📊 indexBasicInfo 格式化结果:', JSON.stringify(formatResponse(result, 'indexBasicInfo'), null, 2));
  // } catch (error) {
  //     console.error('❌ indexBasicInfo 失败:', error.message);
  // }

  // // 测试 indexDetailsData
  // try {
  //     const result = await ball.indexDetailsData(TEST_INDEX_CODE);
  //     console.log('✅ indexDetailsData 返回值为: ', JSON.stringify(result));
  //     console.log('📊 indexDetailsData 格式化结果:', JSON.stringify(formatResponse(result, 'indexDetailsData'), null, 2));
  // } catch (error) {
  //     console.error('❌ indexDetailsData 失败:', error.message);
  // }

  // // 测试 indexWeightTop10
  // try {
  //     const result = await ball.indexWeightTop10(TEST_INDEX_CODE);
  //     console.log('✅ indexWeightTop10 返回值为: ', JSON.stringify(result));
  // } catch (error) {
  //     console.error('❌ indexWeightTop10 失败:', error.message);
  // }

  // // 测试 indexPerf7
  // try {
  //     const result = await ball.indexPerf7(TEST_INDEX_CODE);
  //     console.log('✅ indexPerf7 返回值为: ', JSON.stringify(result));
  //     console.log('📊 indexPerf7 格式化结果:', JSON.stringify(formatResponse(result, 'indexPerf'), null, 2));
  // } catch (error) {
  //     console.error('❌ indexPerf7 失败:', error.message);
  // }

  // // 测试 indexPerf30
  // try {
  //     const result = await ball.indexPerf30(TEST_INDEX_CODE);
  //     console.log('✅ indexPerf30 返回值为: ', JSON.stringify(result));
  //     console.log('📊 indexPerf30 格式化结果:', JSON.stringify(formatResponse(result, 'indexPerf'), null, 2));
  // } catch (error) {
  //     console.error('❌ indexPerf30 失败:', error.message);
  // }

  // // 测试 indexPerf90
  // try {
  //     const result = await ball.indexPerf90(TEST_INDEX_CODE);
  //     console.log('✅ indexPerf90 返回值为: ', JSON.stringify(result));
  //     console.log('📊 indexPerf90 格式化结果:', JSON.stringify(formatResponse(result, 'indexPerf'), null, 2));
  // } catch (error) {
  //     console.error('❌ indexPerf90 失败:', error.message);
  // }
}

async function testUser() {
  console.log("\n👤 测试 User 模块");

  // // 测试 watchList
  // try {
  //     const result = await ball.watchList();
  //     console.log('✅ watchList 返回值为: ', JSON.stringify(result));
  //     console.log('📊 watchList 格式化结果:', JSON.stringify(formatResponse(result, 'watchList'), null, 2));
  // } catch (error) {
  //     console.error('❌ watchList 失败:', error.message);
  // }

  // // 测试 watchStock
  // try {
  //     const result = await ball.watchStock('-1');
  //     console.log('✅ watchStock 返回值为: ', JSON.stringify(result));
  //     console.log('📊 watchStock 格式化结果:', JSON.stringify(formatResponse(result, 'watchStock'), null, 2));
  // } catch (error) {
  //     console.error('❌ watchStock 失败:', error.message);
  // }
}

async function testHkex() {
  console.log("\n🇭🇰 测试 HKEX 模块");

  // // 测试 northboundShareholdingSh
  // try {
  //     const result = await ball.northboundShareholdingSh();
  //     console.log('✅ northboundShareholdingSh 返回值为: ', JSON.stringify(result));
  //     console.log('📊 northboundShareholdingSh 格式化结果:', JSON.stringify(formatResponse(result, 'northboundShareholdingSh'), null, 2));
  // } catch (error) {
  //     console.error('❌ northboundShareholdingSh 失败:', error.message);
  // }

  // // 测试 northboundShareholdingSz
  // try {
  //     const result = await ball.northboundShareholdingSz();
  //     console.log('✅ northboundShareholdingSz 返回值为: ', JSON.stringify(result));
  //     console.log('📊 northboundShareholdingSz 格式化结果:', JSON.stringify(formatResponse(result, 'northboundShareholdingSz'), null, 2));
  // } catch (error) {
  //     console.error('❌ northboundShareholdingSz 失败:', error.message);
  // }

  // 测试 tranCode
  try {
      const result = ball.tranCode('70001');
      console.log('✅ tranCode 返回值为: ', JSON.stringify(result));
      console.log('📊 tranCode 格式化结果:', JSON.stringify(formatResponse(result, 'tranCode'), null, 2));
  } catch (error) {
      console.error('❌ tranCode 失败:', error.message);
  }
}

// 主测试函数
async function runAllTests() {
  console.log("🚀 开始测试 jsnowball 所有模块...\n");

  try {
    // 设置token（如果需要）
    if (ball.hasToken && !ball.hasToken()) {
      console.log("⚠️  未设置token，某些测试可能失败");
    }

    // 运行所有测试
    // await testSuggestStock();
    // await testFinance();
    // await testRealtime();
    // await testCapital();
    // await testF10();
    // await testReport();
    // await testCube();
    // await testFund();
    // await testBond();
    // await testIndexData();
    // await testUser();
    // await testHkex();
  } catch (error) {
    console.error("❌ 测试过程中发生错误:", error.message);
  }
}
// 运行测试
runAllTests();
