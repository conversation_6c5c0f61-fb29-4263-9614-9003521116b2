import mongoose from 'mongoose';

const jsnowballSchema = new mongoose.Schema({
  userId: { type: String, required: true, unique: true },
  token: { type: String },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});

jsnowballSchema.index({ userId: 1 });

const Jsnowball = mongoose.model('Jsnowball', jsnowballSchema);

// Store Jsnowball Token
const storeJsnowballToken = async (userId, token) => {
  await Jsnowball.findOneAndUpdate(
    { userId },
    { token: token, updatedAt: new Date() },
    { upsert: true, setDefaultsOnInsert: true }
  );
};

// Get Jsnowball Token
const getJsnowballToken = async (userId) => {
  const jsnowball = await Jsnowball.findOne({ userId });
  return jsnowball?.token;
};

// Delete Jsnowball Token
const deleteJsnowballToken = async (userId) => {
  await Jsnowball.findOneAndUpdate(
    { userId },
    { $unset: { token: 1 }, updatedAt: new Date() }
  );
};

export {
  storeJsnowballToken,
  getJsnowballToken,
  deleteJsnowballToken
}; 