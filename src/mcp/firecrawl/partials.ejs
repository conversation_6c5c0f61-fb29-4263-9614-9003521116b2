<div class="bg-white shadow rounded-lg p-6 mt-8">
    <h2 class="text-lg font-medium text-gray-900 mb-4"><%= t('firecrawl:title') %></h2>
    <div id="firecrawlKeySection" class="space-y-8">
        <!-- API Key 区块 -->
        <div>
            <h3 class="text-base font-semibold mb-2"><%= t('firecrawl:apiKeyLabel') %></h3>
            <% if (firecrawl.firecrawlApiKey) { %>
                <div class="bg-gray-50 p-4 rounded-md mb-2">
                    <div class="flex items-center space-x-2">
                        <code class="bg-gray-100 px-2 py-1 rounded text-sm font-mono" id="firecrawlKeyCode"><%= firecrawl.firecrawlApiKey %></code>
                        <button onclick="copyFirecrawlKey()" class="text-indigo-600 hover:text-indigo-900 text-sm">
                            <%= t('firecrawl:copy') %>
                        </button>
                    </div>
                </div>
                <button onclick="deleteFirecrawlKey()" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                    <%= t('firecrawl:delete') %>
                </button>
            <% } else { %>
                <form id="setFirecrawlKeyForm" class="flex flex-col sm:flex-row gap-2 items-start sm:items-center">
                    <input type="text" id="firecrawlKeyInput" name="firecrawlApiKey" placeholder="<%= t('firecrawl:apiKeyPlaceholder') %>" class="border px-2 py-1 rounded text-sm w-full sm:w-64" required />
                    <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        <%= t('firecrawl:set') %>
                    </button>
                </form>
            <% } %>
        </div>
        <!-- API URL 区块 -->
        <div style="display: none;">
            <h3 class="text-base font-semibold mb-2"><%= t('firecrawl:apiUrlLabel') %></h3>
            <% if (firecrawl.firecrawlApiUrl) { %>
                <div class="bg-gray-50 p-4 rounded-md mb-2">
                    <div class="flex items-center space-x-2">
                        <code class="bg-gray-100 px-2 py-1 rounded text-sm font-mono" id="firecrawlUrlCode"><%= firecrawl.firecrawlApiUrl %></code>
                        <button onclick="copyFirecrawlUrl()" class="text-indigo-600 hover:text-indigo-900 text-sm">
                            <%= t('firecrawl:copy') %>
                        </button>
                    </div>
                </div>
                <button onclick="deleteFirecrawlUrl()" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                    <%= t('firecrawl:delete') %>
                </button>
            <% } else { %>
                <form id="setFirecrawlUrlForm" class="flex flex-col sm:flex-row gap-2 items-start sm:items-center">
                    <input type="text" id="firecrawlUrlInput" name="firecrawlApiUrl" placeholder="<%= t('firecrawl:apiUrlPlaceholder') %>" class="border px-2 py-1 rounded text-sm w-full sm:w-64" />
                    <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        <%= t('firecrawl:set') %>
                    </button>
                </form>
                <p class="text-xs text-gray-500 mt-1"><%= t('firecrawl:apiUrlHelp') %></p>
            <% } %>
        </div>
    </div>
</div>

<script>
    // Key 相关
    async function deleteFirecrawlKey() {
        if (!confirm('<%= t("firecrawl:deleteConfirm") %>')) return;
        try {
            const response = await fetch('/mcp/firecrawl/key', { method: 'DELETE' });
            if (response.ok) window.location.reload();
        } catch (error) {
            console.error('Error deleting Firecrawl API key:', error);
        }
    }
    function copyFirecrawlKey() {
        const key = document.getElementById('firecrawlKeyCode').textContent;
        navigator.clipboard.writeText(key).then(() => {
            alert('<%= t("firecrawl:copySuccess") %>');
        });
    }
    const setFirecrawlKeyForm = document.getElementById('setFirecrawlKeyForm');
    if (setFirecrawlKeyForm) {
        setFirecrawlKeyForm.addEventListener('submit', async function(e) {
            e.preventDefault();
            const firecrawlApiKey = document.getElementById('firecrawlKeyInput').value;
            try {
                const response = await fetch('/mcp/firecrawl/key', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ firecrawlApiKey })
                });
                if (response.ok) window.location.reload();
            } catch (error) {
                console.error('Error setting Firecrawl API key:', error);
            }
        });
    }
    // URL 相关
    async function deleteFirecrawlUrl() {
        if (!confirm('<%= t("firecrawl:deleteConfirm") %>')) return;
        try {
            const response = await fetch('/mcp/firecrawl/url', { method: 'DELETE' });
            if (response.ok) window.location.reload();
        } catch (error) {
            console.error('Error deleting Firecrawl API url:', error);
        }
    }
    function copyFirecrawlUrl() {
        const url = document.getElementById('firecrawlUrlCode').textContent;
        navigator.clipboard.writeText(url).then(() => {
            alert('<%= t("firecrawl:copyUrlSuccess") %>');
        });
    }
    const setFirecrawlUrlForm = document.getElementById('setFirecrawlUrlForm');
    if (setFirecrawlUrlForm) {
        setFirecrawlUrlForm.addEventListener('submit', async function(e) {
            e.preventDefault();
            const firecrawlApiUrl = document.getElementById('firecrawlUrlInput').value;
            try {
                const response = await fetch('/mcp/firecrawl/url', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ firecrawlApiUrl })
                });
                if (response.ok) window.location.reload();
            } catch (error) {
                console.error('Error setting Firecrawl API url:', error);
            }
        });
    }
</script> 