import mongoose from 'mongoose';

const firecrawlSchema = new mongoose.Schema({
  userId: { type: String, required: true, unique: true },
  apiKey: { type: String },
  apiUrl: { type: String }, // 可选的自定义API URL
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});

firecrawlSchema.index({ userId: 1 });

const Firecrawl = mongoose.model('Firecrawl', firecrawlSchema);

// Store Firecrawl API Key
const storeFirecrawlApiKey = async (userId, firecrawlApiKey, apiUrl = null) => {
  const updateData = { 
    apiKey: firecrawlApiKey, 
    updatedAt: new Date() 
  };
  
  if (apiUrl) {
    updateData.apiUrl = apiUrl;
  }
  
  await Firecrawl.findOneAndUpdate(
    { userId },
    updateData,
    { upsert: true, setDefaultsOnInsert: true }
  );
};

// Get Firecrawl API Key
const getFirecrawlApiKey = async (userId) => {
  const firecrawl = await Firecrawl.findOne({ userId });
  return firecrawl?.apiKey;
};

// Get Firecrawl API URL
const getFirecrawlApiUrl = async (userId) => {
  const firecrawl = await Firecrawl.findOne({ userId });
  return firecrawl?.apiUrl;
};

// Get Firecrawl Config
const getFirecrawlConfig = async (userId) => {
  const firecrawl = await Firecrawl.findOne({ userId });
  return {
    apiKey: firecrawl?.apiKey,
    apiUrl: firecrawl?.apiUrl
  };
};

// Delete Firecrawl API Key
const deleteFirecrawlApiKey = async (userId) => {
  await Firecrawl.findOneAndUpdate(
    { userId },
    { $unset: { apiKey: 1, apiUrl: 1 }, updatedAt: new Date() }
  );
};

export {
  storeFirecrawlApiKey,
  getFirecrawlApiKey,
  getFirecrawlApiUrl,
  getFirecrawlConfig,
  deleteFirecrawlApiKey
}; 