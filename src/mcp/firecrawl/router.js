import express from "express";
import { storeFirecrawl<PERSON><PERSON><PERSON><PERSON>, deleteFirecrawl<PERSON>pi<PERSON><PERSON>, getFirecrawlApi<PERSON><PERSON>, getFirecrawlApiUrl } from "./dao.js";
import mongoose from 'mongoose';

const router = express.Router();

// 设置 Firecrawl API Key
router.post("/firecrawl/key", async (req, res) => {
  const { firecrawlApiKey } = req.body;
  if (!firecrawlApiKey) return res.status(400).json({ error: "Firecrawl API Key required" });
  await storeFirecrawlApiKey(req.session.user.id, firecrawlApiKey, undefined);
  res.status(200).json({ firecrawlApiKey });
});

// 删除 Firecrawl API Key
router.delete("/firecrawl/key", async (req, res) => {
  // 只删除key，保留url
  await mongoose.model('Firecrawl').findOneAndUpdate(
    { userId: req.session.user.id },
    { $unset: { apiKey: 1 }, updatedAt: new Date() }
  );
  res.status(204).send();
});

// 设置 Firecrawl API URL
router.post("/firecrawl/url", async (req, res) => {
  const { firecrawlApiUrl } = req.body;
  if (!firecrawlApiUrl) return res.status(400).json({ error: "Firecrawl API URL required" });
  await mongoose.model('Firecrawl').findOneAndUpdate(
    { userId: req.session.user.id },
    { apiUrl: firecrawlApiUrl, updatedAt: new Date() },
    { upsert: true, setDefaultsOnInsert: true }
  );
  res.status(200).json({ firecrawlApiUrl });
});

// 删除 Firecrawl API URL
router.delete("/firecrawl/url", async (req, res) => {
  // 只删除url，保留key
  await mongoose.model('Firecrawl').findOneAndUpdate(
    { userId: req.session.user.id },
    { $unset: { apiUrl: 1 }, updatedAt: new Date() }
  );
  res.status(204).send();
});

export default router; 