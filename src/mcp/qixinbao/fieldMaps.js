// 企信宝API字段映射表

// qixin_basic_info字段名到描述的映射表
export const QIXIN_BASIC_INFO_FIELD_MAP = {
  econKind: "企业类型",
  registCapi: "注册资本",
  id: "企业id",
  tags: "企业标签",
  belongOrg: "所属工商局",
  status: "经营状态（原始）",
  termStart: "营业开始日期",
  format_name: "标准企业名称",
  historyNames: "历史名称",
  revoke_date: "吊销日期",
  endDate: "注销日期",
  regNo: "企业注册号",
  econKindCode: "企业类型代码",
  domain: "四级行业",
  categoryNew: "企业二级分类",
  address: "地址",
  orgNo: "组织机构号",
  districtCode: "地区代码",
  startDate: "成立日期",
  scope: "经营范围",
  name: "企业名称（国家公示）",
  creditNo: "统一社会信用代码",
  new_status: "经营状态（清洗后）",
  operType: "企业法定代表人类型",
  operPid: "企业法定代表人标识",
  operName: "企业法定代表人",
  title: "公司代表人职务",
  checkDate: "核准日期",
  actualCapi: "实缴资本",
  termEnd: "营业结束日期",
  currency_unit: "货币单位",
  revoke_reason: "吊销原因",
  type_new: "企业大类",
  logout_reason: "注销原因",
  is_history: "是否历史",
  fenname: "企业英文名",
  sign: "数据签名",
  status: "返回结果状态",
  message: "返回结果消息"
};

// qixin_enterprise_info字段名到描述的映射表
export const QIXIN_ENTERPRISE_INFO_FIELD_MAP = {
  // ent_info 主体
  eid: "企业唯一标识id",
  credit_no: "统一社会信用代码",
  name: "企业名称",
  oper_name: "法定代表人",
  title: "公司代表人职务",
  status: "登记状态",
  start_date: "成立日期",
  regist_capi: "注册资本",
  actual_capi: "实缴资本",
  org_no: "组织机构代码",
  reg_no: "工商注册号",
  tax_num: "纳税人识别号",
  econ_kind: "企业类型",
  organization_code: "组织分类代码",
  term_start: "开始营业期限",
  term_end: "结束营业期限",
  qualification: "纳税人资质",
  collegues_num: "人员规模",
  canbao_count: "参保人数",
  check_date: "核准日期",
  belong_org: "登记机关",
  import_export_num: "进出口企业代码",
  fenname: "英文名",
  address: "注册地址",
  scope: "经营范围",
  brief: "简介",
  logo_url: "logo地址",
  is_small: "是否为小微企业库企业",
  scale: "企业规模",
  is_history: "工商是否下架",
  is_listed: "是否上市",
  group_name: "所属集团",

  // district_info
  district_info: "区域信息",
  province: "省份",
  city: "城市",
  district: "区县",
  district_code: "所属区域代码",

  // industry_info
  industry_info: "行业信息",
  indu_code_l1: "一级行业代码",
  indu_l1: "一级行业描述",
  indu_code_l2: "二级行业代码",
  indu_l2: "二级行业描述",
  indu_code_l3: "三级行业代码",
  indu_l3: "三级行业描述",
  indu_code_l4: "四级行业代码",
  indu_l4: "四级行业描述",

  // tags_list
  tags_list: "企业画像",
  tag_id: "标签id",
  tag_name: "标签名称",

  // judicial_cases_list
  judicial_cases_list: "案件串联列表",
  case_name: "案件名称",
  case_type: "案件类型",
  role_min_date: "案件身份",
  case_cause_min_date: "案由",
  case_no: "案号",
  court: "法院",
  newest_base_type: "最新审理程序",

  // casecount_list
  casecount_list: "诉讼关系列表",
  related_name: "对方当事人",
  cases_count: "关联司法案件数量",
  main_cause: "主要案由",

  // cases_list
  cases_list: "立案信息",
  related_companies: "当事人",
  type: "当事人类型",
  item: "当事人id",
  name: "当事人姓名",
  role: "当事人角色",
  clean_role: "清洗后角色",
  case_cause: "案由",
  start_date: "立案时间",
  hearing_date: "开庭时间",
  end_date: "结束时间",
  case_status: "案件状态",

  // hearing_notices_list
  hearing_notices_list: "开庭公告列表",
  pure_role: "身份",
  cause_action: "案由",

  // court_notices_list
  court_notices_list: "法院公告列表",
  people: "当事人",
  date: "发布时间",
  content: "公告内容",

  // send_notices_list
  send_notices_list: "送达公告",
  case_reason: "案由",

  // judgement_list
  judgement_list: "裁判文书列表",
  judgeresult: "文书类型/结果",
  clean_role: "案件身份",
  trial_result: "审判结果",
  title: "案件名称",
  case_reasons: "案由",
  related_companies: "身份",
  by: "方式",
  standard_case_no: "案号",

  // enforcement_list
  enforcement_list: "被执行人列表",
  amount: "执行标的",
  court_name: "执行法院",
  case_date: "立案时间",

  // executions_list
  executions_list: "失信信息列表",
  case_number: "案号",
  doc_number: "执行根据文号",
  publish_date: "发布日期",
  execution_status: "被执行人情况",

  // judicial_freeze_list
  judicial_freeze_list: "股权冻结列表",
  detail_notice_no: "执行通知书文号",
  be_executed_person: "被执行人",
  object_company: "冻结股权的标的企业",
  executive_court: "执行法院",
  detail_public_date: "公示日期",

  // restricted_consumer_list
  restricted_consumer_list: "限制高消费列表",
  case_reason: "案由",
  filing_date: "立案时间",
  release_date: "限制令发布日期",

  // exit_limitation_list
  exit_limitation_list: "限制出境",
  pname: "限制出境对象",
  ename: "被执行人",
  url: "内容",

  // bidding_limitation_list
  bidding_limitation_list: "限制招投标列表",
  case_reason: "执行案由",
  unexecuted_amount: "未执行金额（元）",

  // terminationcaseitem_list
  terminationcaseitem_list: "终本案件列表",
  case_no_terminal: "案号",
  court: "执行法院",
  terminate_date: "终本时间",
  fail_perform_amount: "未履行金额（元）",

  // auctions_list
  auctions_list: "司法拍卖列表",
  full_name: "拍卖标的",
  start_price: "起拍价",
  description: "拍品介绍",

  // abnormal_list
  abnormal_list: "经营异常",
  in_date: "列入日期",
  department: "做出决定机关（列入）",
  in_reason: "列入经营异常名录原因",
  out_date: "移出日期",
  out_department: "做出决定机关（移出）",
  out_reason: "移出经营异常名录原因",

  // serious_illegal_list
  serious_illegal_list: "严重违法失信",
  ill_type: "违法类别",
  in_reason: "列入原因",
  in_department: "做出决定机关（列入）",
  out_reason: "移出原因",
  out_department: "做出决定机关（移出）",

  // administrative_punishment_list
  administrative_punishment_list: "行政处罚列表",
  number: "行政处罚决定书文号",
  illegal_type: "违法行为类型",
  content: "行政处罚内容",
  punish_amount: "罚款金额",
  district_name: "作出行政处罚决定机关名称",
  date: "作出行政处罚决定日期",

  // environment_punishment_list
  environment_punishment_list: "环保处罚",
  punish_date: "处罚日期",
  document_no: "决定文书号",
  punishment_result: "处罚内容",
  punish_amnt: "罚款金额",
  standard_dep: "做出处罚决定机关",

  // labor_arbitration_list
  labor_arbitration_list: "劳动仲裁",
  case_reason: "案由",
  release_time: "发布日期",

  // black_list
  black_list: "黑名单",
  lists_type: "黑名单类型",
  black_basis: "黑名单认定依据",
  maintain_department: "认定部门",
  department_level: "认定等级",
  in_lists_date: "列入日期",
  details: "内容",
  punishment_result: "处罚结果",
  data_from: "数据来源",

  // bankruptcy_list
  bankruptcy_list: "破产案件",
  case_kind: "案件类型",
  app_name: "申请人",

  // clear_account_list
  clear_account_list: "清算信息",
  leader: "清算组负责人",
  employees: "清算组成员",

  // equityquality_list
  equityquality_list: "股权出质列表",
  number: "登记编号",
  pledgor: "出质人",
  pawnee: "质权人",
  object_company: "标的方",

  // mortages_list
  mortages_list: "动产抵押",
  number: "登记编号",
  department: "登记机关",
  guarantees: "抵押物",
  type: "被担保债权种类",
  amount: "被担保债权数额",
  period: "债务人履行债务的期限",

  // overduetaxs_list
  overduetaxs_list: "欠税信息",
  taxpayer_num: "纳税人识别号",
  overdue_type: "欠税税种",
  overdue_amount: "欠税金额",
  curr_overdue_amount: "当前新发生的欠税额",
  pub_date: "发布时间",
  pub_department: "发布单位",

  // huge_tax_punishment_list
  huge_tax_punishment_list: "重大税收违法",
  time: "发布时间",
  pub_department: "所属税务机关",
  case_type: "案件性质",
  url: "详情",

  // abnormal_enterprises_list
  abnormal_enterprises_list: "非正常户",
  judge_date: "认定日期",
  tax_num: "纳税人识别号",
  judge_department: "认定单位",

  // knowledge_properites_list
  knowledge_properites_list: "知识产权出质",
  name: "名称",
  pledgor: "出质人",
  pawnee: "质权人",
  public_date: "公示日期",

  // simple_cancellation_list
  simple_cancellation_list: "简易注销",
  notice_period_start: "公示开始日期",
  notice_period_end: "公示结束日期",
  result: "简易注销结果",

  // logout_announcement_list
  logout_announcement_list: "注销备案",
  audit_reg_date: "清算组备案日期",
  creditor_start_date: "债权人公告开始日期",
  creditor_end_date: "债权人公告结束日期",
  logout_reason: "注销原因",

  // change_list
  change_list: "变更记录",
  change_date: "变更日期",
  change_item: "变更事项",
  before_content: "变更前",
  after_content: "变更后",

  // 其他通用字段
  group_info: "所属集团",
  group_eid: "集团eid",
  group_logo: "集团logo",
  contract_info: "联系方式",
  addresses_list: "全部地址",
  source: "地址来源",
  telephones_list: "全部电话",
  telephone: "电话",
  websites_list: "全部网址",
  web_url: "网址",
  emails_list: "全部邮箱",
  email: "邮箱",
  employees_list: "主要人员信息",
  partners_list: "股东信息(工商登记股东）",
  stock_name: "股东名称",
  stock_id: "股东id",
  stock_type: "股东类型",
  stock_percent: "持股比例",
  country: "国家/地区",
  total_should_capi: "认缴出资",
  total_real_capi: "实缴出资",
  pub_partners_list: "股东信息(最新公示股东）",
  share_type: "股份类型",
  should_capi: "认缴额（万）",
  con_date: "认缴出资日期",
  real_capi: "实缴额（万）",
  real_capi_date: "实缴出资日期",
  currency_code: "币种代码",
  stock_num: "持股数",
  invest_list: "对外投资信息",
  invest_name: "被投资企业名称",
  invest_status: "被投资企业状态",
  invest_eid: "被投资公司id",
  invest_oper_name: "被投资企业法定代表人",
  invest_regist_capi: "被投资企业注册资本",
  invest_start_date: "被投资企业成立日期",
  invest_credit_no: "被投资公司统一社会信用代码",
  invest_reg_no: "被投资公司注册号",
  invest_quote_status: "被投资公司上市状态",
  should_con_date: "认缴缴出资日期",
  change_record_list: "变更记录",
  branches_list: "分支机构",
  sub_eid: "分支机构eid",
  actual_controller_info: "实际控制人",
  entity_name: "实控人名称/受益所有人名称",
  entity_eid: "实际控制人id/受益人id",
  total_percent: "实控人总持股/投资比例",
  path: "实控人关系路径/持股路径",
  benefit_list: "受益所有人",
  entity_type: "受益所有人类型",
  history_name_list: "历史名称",
  history_name: "历史公司名/曾用名",
  logout_info: "注销吊销信息",
  listed_info: "上市信息",
  listed_type: "上市类型",
  liststate: "上市状态",
  securitycode: "证券代码",
  headquarters_info: "所属总公司",
  ename: "总公司名称/企业英文名",
  coordinate_info: "坐标信息",
  longitude: "经度",
  latitude: "纬度",
  taxrating_list: "纳税评级信息",
  level: "评级等级",
  year: "获得年份",
  agency: "评级单位",
  license_list: "行政许可信息",
  domain_list: "网站域名备案",
  home_url: "网站首页网址",
  domain: "网站域名",
  checkups_list: "抽查检查信息",
  financing_list: "融资信息",
  round_date: "融资时间",
  round: "融资轮次",
  amount: "融资金融",
  investors: "投资人",
  logout_list: "简易注销信息",
  gs_sca_result: "简易注销结果",
  notice_period_start: "公告开始日期",
  notice_period_end: "公告结束日期",
  gs_sca_objections: "异议信息",
  logout_record_list: "注销备案",
  audit_reg_date: "清算组备案日期",
  audit_start_date: "清算组成立日期",
  audit_address: "清算组办公地址",
  audit_leader: "清算组负责人",
  audit_employees: "清算组成员",
  creditor_start_date: "债权人公告开始日期",
  creditor_end_date: "债权人公告结束日期",
  creditor_announcement: "公告内容",
  creditor_person: "债权申报联系人",
  creditor_address: "债权申报地址",
  cancel_date: "终止注销决议日期",
  status: "状态",
};

// qixin_person_info字段名到描述的映射表
export const QIXIN_PERSON_INFO_FIELD_MAP = {
  // basic_info
  basic_info: "基本信息",
  
  // partners_list
  partners_list: "合作伙伴",
  pname: "合作伙伴姓名",
  cname: "任职企业",
  start_date: "企业注册日期",
  status: "经营状态",
  job_title: "职位",
  
  // oper_list
  oper_list: "担任法人的企业列表",
  name: "法定代表人",
  pid: "人员id",
  company: "企业名称",
  credit_no: "企业统一社会信用代码/注册号",
  eid: "企业id",
  regist_date: "企业注册日期",
  company_type: "企业类型",
  company_status: "企业经营状态",
  regist_capi: "企业注册资本",
  
  // stockholder_list
  stockholder_list: "担任股东的企业列表",
  oper_name: "企业法人",
  oper_pid: "人员id",
  should_capi: "认缴出资",
  percent: "出资比例",
  
  // manager_list
  manager_list: "担任高管的企业列表",
  title: "职务",
  
  // investment_list
  investment_list: "控股企业列表",
  invest_name: "被投资企业名称",
  invest_eid: "被投资企业eid",
  invest_oper_name: "被投资企业法定代表人",
  invest_credit_no: "被投资企业统一社会信用代码",
  invest_status: "被投资企业状态",
  invest_start_date: "被投资企业成立日期",
  invest_percent: "总投资比例",
  direct_percent: "直接投资比例",
  invest_level: "最短投资层级",
  invest_desc: "投资描述",
  invest_relationship_path: "投资路径",
  invest_path_count: "投资路径数量",
  his_direct_invest: "是否历史直接投资",
  end_date: "历史投资退出日期",
  
  // actual_controller_list
  actual_controller_list: "实控企业列表",
  control_ename: "实控企业名称",
  control_eid: "实控企业eid",
  total_percent: "持股比例",
  control_credit_no: "实控企业统一社会信用代码",
  control_path: "实控人路径",
  control_reg_no: "实控企业注册号",
  control_status: "实控企业状态",
  control_regist_capi: "实控企业注册资本",
  control_oper_name: "实控企业法定代表人",
  
  // risk_info
  risk_info: "风险信息",
  
  // restricted_consumer_list
  restricted_consumer_list: "限制高消费列表",
  case_no: "案号",
  court: "执行法院",
  case_reason: "案由",
  filing_date: "立案时间",
  release_date: "限制令发布日期",
  content: "内容",
  source: "地址",
  
  // judicial_freeze_list
  judicial_freeze_list: "股权冻结",
  company_name: "冻结股权标的企业",
  number: "执行通知书文号",
  amount: "股权数额",
  executive_court: "执行法院",
  status: "类型/状态",
  detail_freeze_start_date: "股权冻结开始时间",
  detail_freeze_end_date: "股权冻结结束时间",
  
  // equity_pledge_list
  equity_pledge_list: "股权出质",
  pledgor: "出质人",
  pawnee: "质权人",
  object_company: "出质股权标的企业",
  pledgor_amount: "出质股权数额（万元）",
  date: "登记日期",
  
  // equity_mortgage_list
  equity_mortgage_list: "股权质押",
  noticedate: "公告日期",
  p_name: "人员姓名",
  frozentype: "质押冻结类型",
  sharetype: "质押股份类型",
  frozenstartdate: "质押/冻结起始日",
  freedate: "质押/冻结截止日",
  ghdate: "过户日期",
  freedtaeinad: "实际解除日期",
  frozenname: "实施质押冻结机构",
  frozeneid: "质押冻结机构代码",
  invamount: "涉及金额",
  sharehdnum: "所持股数（万股）",
  achgshare: "变动后股数",
  sharefrozennum: "质押/冻结股数（万股）",
  noreleasnum: "剩余未解押数",
  amtsharefrozen: "累计质押股数（万股）",
  frozenratio: "质押/冻结占所持股比例",
  frozenintotal: "质押/冻结占总股本比例",
  achgratio: "变动后股数占总股本比例",
  amtsharerepo: "累计质押/约定回购股数（万股）",
  amtshratio: "累计质押数占所持股比例",
  amtfrozenratio: "累计质押股数占总股本比例",
  amtreporatio: "累计质押/约定回购股数占总股本比例",
  ndateamtsharerepo: "截止公告日累计质押数（股）",
  ndateamtshatoeqra: "截止公告日累计质押数占总股本比例",
  ndateamtshratio: "截止公告日累计质押数占所持股比例",
  frozendateexplain: "质押/冻结期限描述",
  pledgepur: "质押目的",
  meunfrpled: "解除质押冻结方式",
  frozprogres: "质押冻结进度",
  relinfocode: "公告编码",
  frozenreason: "质押/冻结事由",
  remark: "备注",
  
  // related_enterprise_risk_info
  related_enterprise_risk_info: "关联企业风险",
  
  // judgement_list
  judgement_list: "裁判文书列表",
  position: "投资及任职关系",
  num: "数量",
  
  // enforcement_list
  enforcement_list: "被执行人列表",
  
  // execution_list
  execution_list: "失信信息列表",
  
  // consumption_restriction_list
  consumption_restriction_list: "限制高消费列表",
  
  // final_case_list
  final_case_list: "终本案件列表",
  // is_history
  is_history: "是否历史",
};

// qixin_lawsuit_summary字段名到描述的映射表
export const QIXIN_LAWSUIT_SUMMARY_FIELD_MAP = {
  fygg: "法院公告信息数量",
  shixin: "失信公告信息数量",
  ktgg: "开庭公告信息数量",
  consumerResult: "限制高消费数量",
  total: "所有信息数量",
  cpws: "裁判文书信息数量",
  terminationCaseResult: "终本案件数量",
  lian: "立案信息数量",
  zxgg: "执行公告信息数量",
  sign: "数据签名",
  status: "返回结果状态",
  message: "返回结果消息"
}; 