import z from "zod";
import axios from "axios";
import {
  QIXIN_BASIC_INFO_FIELD_MAP,
  QIXIN_ENTERPRISE_INFO_FIELD_MAP,
  QIXIN_PERSON_INFO_FIELD_MAP,
  QIXIN_LAWSUIT_SUMMARY_FIELD_MAP,
} from "./fieldMaps.js";

// 工具函数：获取企信宝API配置
function getQixinConfig() {
  const appKey = "37fccb47-f82b-42d2-a40a-ab4393b71cca";

  const secretKey = "b00c4bdf-f003-4720-94c7-476dd2b6cebf";
  const baseUrl = "https://api.qixin.com/APIService";

  if (!appKey || !secretKey) {
    throw new Error("QIXIN_APP_KEY 和 QIXIN_SECRET_KEY 环境变量必须设置");
  }

  return { appKey, secretKey, baseUrl };
}

// 企信宝状态码映射
const QIXIN_STATUS_CODES = {
  200: "查询成功",
  201: "查询无结果",
  202: "查询正在进行中，请稍后再试",
  203: "数据正在更新，请稍后查询",
  207: "查询发生错误，请联系技术支持以获取帮助",
  208: "请求参数错误或为空，请检查您的输入是否正确",
  209: "接口查询异常，请联系技术支持以获取帮助",
  213: "今日调用次数已达上限，请明天再试或联系升级",
  214: "API密钥鉴权失败，请检查您的AppKey和SecretKey是否正确",
  216: "账户调用次数已达总额度上限，请联系升级",
  101: "AppKey无效，请检查您的AppKey配置",
  102: "账户余额不足，请及时充值",
  103: "AppKey已被停用，请联系技术支持",
  104: "IP白名单未设置或当前IP不在允许列表中，请检查您的IP白名单配置",
  105: "当前AppKey未授权调用此接口，请联系技术支持",
  109: "接口暂时不可用或已被停用，请联系技术支持",
  110: "您的账户已过期，请续费后使用",
  113: "您的账户尚未激活，请激活后使用",
};

function formatObject(
  obj,
  fieldMap,
  indent = "",
  maxDepth = 5,
  currentDepth = 0
) {
  // 防止无限递归
  if (currentDepth >= maxDepth) {
    return `${indent}[数据层级过深，已省略]`;
  }

  return Object.entries(obj)
    .map(([key, value]) => {
      const label = fieldMap[key] || key;

      if (
        typeof value === "object" &&
        value !== null &&
        !Array.isArray(value)
      ) {
        // 递归处理对象
        return `${indent}${label}：\n${formatObject(
          value,
          fieldMap,
          indent + "  ",
          maxDepth,
          currentDepth + 1
        )}`;
      }

      if (Array.isArray(value)) {
        if (value.length === 0) return `${indent}${label}：-`;

        // 显示所有数组项
        return (
          `${indent}${label}：\n` +
          value
            .map((item, idx) => {
              if (typeof item === "object" && item !== null) {
                return `${indent}  [${idx + 1}] ${formatObject(
                  item,
                  fieldMap,
                  indent + "    ",
                  maxDepth,
                  currentDepth + 1
                )}`;
              } else {
                return `${indent}  [${idx + 1}] ${item ?? "-"}`;
              }
            })
            .join("\n")
        );
      }

      return `${indent}${label}：${value ?? "-"}`;
    })
    .join("\n");
}

// 通用企信宝API调用函数
async function fetchQixin(path, queryParams) {
  const { appKey, secretKey, baseUrl } = getQixinConfig();

  const url = new URL(`${baseUrl}${path}`);
  Object.entries(queryParams).forEach(([key, value]) => {
    if (value !== undefined) {
      url.searchParams.set(key, value.toString());
    }
  });
  url.searchParams.set("appkey", appKey);
  url.searchParams.set("secret_key", secretKey);

  try {
    const response = await axios.get(url.toString());
    const responseData = response.data;

    if (responseData.status === "200") {
      return {
        content: [
          { type: "text", text: JSON.stringify(responseData.data ?? null) },
        ],
      };
    } else {
      const userMessage =
        QIXIN_STATUS_CODES[responseData.status] ||
        responseData.message ||
        `未知业务状态码: ${responseData.status}`;
      throw new Error(userMessage);
    }
  } catch (error) {
    if (error.response) {
      throw new Error(
        `企信宝API调用失败: ${error.response.status} ${error.response.statusText}`
      );
    }
    throw new Error(`企信宝API调用失败: ${error.message}`);
  }
}

// 通用数据处理函数
function processQixinResponse(
  raw,
  fieldMap,
  notFoundMessage = "未找到相关数据"
) {
  let data;
  try {
    data = JSON.parse(raw.content[0].text);
  } catch {
    return { content: [{ type: "text", text: "查询结果解析失败" }] };
  }

  if (!data) {
    return { content: [{ type: "text", text: notFoundMessage }] };
  }

  const summary = formatObject(data, fieldMap);
  return {
    content: [{ type: "text", text: summary }],
  };
}

// 企信宝工具定义
const qixinTools = [
  {
    name: "qixin_adv_search",
    description:
      "根据关键词对企业进行模糊搜索（注意，首先用这个接口找到公司的全名）",
    inputSchema: {
      keyword: z
        .string()
        .describe(
          '企业相关关键字，输入字数>=2且不能仅输入"公司"或"有限公司"（必填）'
        ),
      matchType: z
        .string()
        .optional()
        .describe(
          "匹配类型（可选），keyword 需要匹配的 字段，可以输入以下一个或者几个类型， 多个类型使用,分隔： partner：股东 oper：法人 member：高管 contact：联系方式 scope：经营范围 ename：公司名称 patent：专利 copyright：著作权作品名称 software：软件著作权名称 trademark：商标 domain：网址 product：产品"
        ),
      region: z
        .string()
        .optional()
        .describe(
          "地区编码（可选），输入参数为空返回全部区域匹配记录。可传入省/直辖市，城市，区县的国家区划码。只支持传入一个区划码。省/直辖市：传入国家区划码前两位数字，如搜索江苏省则传入32，各省/直辖市编码可参考本文档附录。城市：传入国家区划码前四位数字，如搜索苏州市，则传入3205。区县：传入国家区划码六位数字，如搜索苏州市吴中区，则传入320506。可以通过下面网页查询国家区划编码：https://www.mca.gov.cn/mzsj/xzqh/2022/202201xzqh.html"
        ),
      skip: z
        .number()
        .optional()
        .describe("跳过条目数（默认0，单页返回10条数据）"),
    },
    async handler(userId, params) {
      const raw = await fetchQixin("/v2/search/advSearch", {
        keyword: params.keyword,
        matchType: params.matchType,
        region: params.region,
        skip: params.skip || 0,
      });

      let data;
      try {
        data = JSON.parse(raw.content[0].text);
      } catch {
        return { content: [{ type: "text", text: "查询结果解析失败" }] };
      }

      if (!data || !data.items || data.items.length === 0) {
        return { content: [{ type: "text", text: "未找到相关企业" }] };
      }

      // 组织企业列表摘要
      const summary = data.items
        .map((item, idx) => {
          return (
            `${idx + 1}. 企业名称：${item.name}\n` +
            `   注册号：${item.reg_no}\n` +
            `   法定代表人：${item.oper_name}\n` +
            `   成立日期：${item.start_date}\n` +
            `   统一社会信用代码：${item.credit_no}\n` +
            `   类型：${item.type}\n`
          );
        })
        .join("\n");

      return {
        content: [
          {
            type: "text",
            text: `共${data.total}条，当前返回${data.num}条：\n${summary}`,
          },
        ],
      };
    },
  },
  {
    name: "qixin_basic_info",
    description:
      "【工商照面】企业工商照面及相关信息，包括统一社会信用代码、注册资本、经营范围、企业法定代表人等",
    inputSchema: {
      keyword: z.string().describe("企业全名/注册号/统一社会信用代码（必填）"),
    },
    async handler(userId, params) {
      const raw = await fetchQixin("/enterprise/getBasicInfo", {
        keyword: params.keyword,
      });
      return processQixinResponse(
        raw,
        QIXIN_BASIC_INFO_FIELD_MAP,
        "未找到相关企业"
      );
    },
  },
  {
    name: "qixin_enterprise_info",
    description:
      "【企业信息全面排查】通过企业名称/统代一键获取企业工商信息、集团信息、企业标签、上市信息、经营等企业画像信息",
    inputSchema: {
      keyword: z.string().describe("企业全名/注册号/统一社会信用代码（必填）"),
      is_history: z.string().describe("是否查询历史信息，0：否，1：是"),
    },
    async handler(userId, params) {
      const raw = await fetchQixin("/reportData/getAllEntInfoByName", {
        keyword: params.keyword,
        is_history: params.is_history,
      });
      return processQixinResponse(
        raw,
        QIXIN_ENTERPRISE_INFO_FIELD_MAP,
        "未找到相关企业"
      );
    },
  },
  {
    name: "qixin_risk_info",
    description:
      "【企业综合风险排查】通过企业名称/统代一键获取企业在工商、司法、税务等各类风险信息",
    inputSchema: {
      keyword: z.string().describe("企业全名/注册号/统一社会信用代码（必填）"),
      is_history: z.string().describe("是否查询历史信息，0：否，1：是"),
    },
    async handler(userId, params) {
      const raw = await fetchQixin("/reportData/getAllRiskInfoByName", {
        keyword: params.keyword,
        is_history: params.is_history,
      });
      return processQixinResponse(
        raw,
        QIXIN_ENTERPRISE_INFO_FIELD_MAP,
        "未找到相关企业风险信息"
      );
    },
  },
  {
    name: "qixin_person_info",
    description:
      "【企业董监高信息洞察】通过企业名称加人名获取董监高相关信息，包括个人任职、投资情况、风险信息等",
    inputSchema: {
      keyword: z.string().describe("企业全名/注册号/统一社会信用代码（必填）"),
      p_name: z.string().describe("人员姓名（必填）"),
      is_history: z.string().describe("是否查询历史信息，0：否，1：是"),
    },
    async handler(userId, params) {
      const raw = await fetchQixin("/reportData/getAllPersonInfo", {
        keyword: params.keyword,
        p_name: params.p_name,
        is_history: params.is_history,
      });
      return processQixinResponse(
        raw,
        QIXIN_PERSON_INFO_FIELD_MAP,
        "未找到相关人员信息"
      );
    },
  },
  {
    name: "qixin_lawsuit_summary",
    description:
      "【整体诉讼统计信息】裁判文书、开庭公告、执行公告、失信公告、法院公告、立案信息、限制高消费、终本案件等",
    inputSchema: {
      name: z.string().describe("企业全名/统一社会信用代码（必填）"),
    },
    async handler(userId, params) {
      const raw = await fetchQixin("/sumLawsuit/sumLawsuit", {
        name: params.name,
      });
      return processQixinResponse(
        raw,
        QIXIN_LAWSUIT_SUMMARY_FIELD_MAP,
        "未找到相关诉讼统计信息"
      );
    },
  },
];

export { qixinTools };
