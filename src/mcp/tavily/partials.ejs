<div class="bg-white shadow rounded-lg p-6 mt-8">
    <h2 class="text-lg font-medium text-gray-900 mb-4"><%= t('tavily:title') %></h2>
    <div id="tavilyKeySection" class="space-y-4">
        <% if (tavily.tavilyApiKey) { %>
            <div class="bg-gray-50 p-4 rounded-md">
                <p class="text-sm text-gray-600 mb-2"><%= t('tavily:yourKey') %></p>
                <div class="flex items-center space-x-2">
                    <code class="bg-gray-100 px-2 py-1 rounded text-sm font-mono" id="tavilyKeyCode"><%= tavily.tavilyApiKey %></code>
                    <button onclick="copyTavilyKey()" class="text-indigo-600 hover:text-indigo-900 text-sm">
                        <%= t('tavily:copy') %>
                    </button>
                </div>
            </div>
            <button onclick="deleteTavilyKey()" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                <%= t('tavily:delete') %>
            </button>
        <% } else { %>
            <form id="setTavilyKeyForm" class="flex flex-col sm:flex-row gap-2 items-start sm:items-center">
                <input type="text" id="tavilyKeyInput" name="tavilyApiKey" placeholder="Tavily API Key" class="border px-2 py-1 rounded text-sm w-full sm:w-64" required />
                <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    <%= t('tavily:set') %>
                </button>
            </form>
        <% } %>
    </div>
</div>

<script>
    async function deleteTavilyKey() {
        if (!confirm('<%= t("tavily:deleteConfirm") %>')) return;
        try {
            const response = await fetch('/mcp/tavily/key', { method: 'DELETE' });
            if (response.ok) window.location.reload();
        } catch (error) {
            console.error('Error deleting Tavily API key:', error);
        }
    }
    function copyTavilyKey() {
        const key = document.getElementById('tavilyKeyCode').textContent;
        navigator.clipboard.writeText(key).then(() => {
            alert('<%= t("tavily:copySuccess") %>');
        });
    }
    const setTavilyKeyForm = document.getElementById('setTavilyKeyForm');
    if (setTavilyKeyForm) {
        setTavilyKeyForm.addEventListener('submit', async function(e) {
            e.preventDefault();
            const tavilyApiKey = document.getElementById('tavilyKeyInput').value;
            try {
                const response = await fetch('/mcp/tavily/key', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ tavilyApiKey })
                });
                if (response.ok) window.location.reload();
            } catch (error) {
                console.error('Error setting Tavily API key:', error);
            }
        });
    }
</script> 