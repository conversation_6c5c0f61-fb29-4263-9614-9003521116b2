import express from "express";
import { storeTavily<PERSON><PERSON><PERSON><PERSON>, deleteTavily<PERSON><PERSON><PERSON><PERSON> } from "./dao.js";

const router = express.Router();

// Tavily API Key routes
router.post("/tavily/key", async (req, res) => {
  const { tavilyApiKey } = req.body;
  if (!tavilyApiKey) return res.status(400).json({ error: "Tavily API Key required" });
  await storeTavilyApiKey(req.session.user.id, tavilyApiKey);
  res.status(200).json({ tavilyApiKey });
});

router.delete("/tavily/key", async (req, res) => {
  await deleteTavilyApiKey(req.session.user.id);
  res.status(204).send();
});

export default router; 