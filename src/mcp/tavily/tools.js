import z from "zod";
import { tavily } from "@tavily/core";
import { getTavily<PERSON>pi<PERSON><PERSON> } from "./dao.js";

// 工具函数：获取 Tavily 客户端
async function getTavilyClient(userId) {
  const apiKey = await getTavilyApiKey(userId);
  if (!apiKey) {
    throw new Error("Tavily API key not found for user");
  }
  return tavily({ apiKey });
}

// Tavily tool definitions
const tavilyTools = [
  {
    name: "tavily_search",
    description: "Execute a search query using Tavily Search",
    inputSchema: {
      query: z.string().describe("Enter your search query or question"),
      options: z
        .object({
          searchDepth: z
            .enum(["basic", "advanced"])
            .optional()
            .describe(
              "Search depth: basic (simple search) or advanced (in-depth search)"
            ),
          topic: z
            .enum(["general", "news", "finance"])
            .optional()
            .describe(
              "Search topic: general (all topics), news (news only), finance (financial content)"
            ),
          days: z
            .number()
            .optional()
            .describe("Limit search to recent days, e.g.: 7 for last 7 days"),
          maxResults: z
            .number()
            .optional()
            .describe(
              "Maximum number of results to return, e.g.: 10 for 10 results"
            ),
          includeImages: z
            .boolean()
            .optional()
            .describe("Include images in results: true or false"),
          includeImageDescriptions: z
            .boolean()
            .optional()
            .describe("Include image descriptions: true or false"),
          includeAnswer: z
            .boolean()
            .optional()
            .describe("Include AI-generated answer summary: true or false"),
          includeRawContent: z
            .boolean()
            .optional()
            .describe("Include raw webpage content: true or false"),
          includeDomains: z
            .array(z.string())
            .optional()
            .describe(
              "Only search within these domains, e.g.: ['example.com', 'test.com']"
            ),
          excludeDomains: z
            .array(z.string())
            .optional()
            .describe(
              "Exclude these domains from search, e.g.: ['example.com', 'test.com']"
            ),
          maxTokens: z
            .number()
            .optional()
            .describe("Maximum number of tokens in response, e.g.: 1000"),
          timeRange: z
            .enum(["year", "month", "week", "day", "y", "m", "w", "d"])
            .optional()
            .describe(
              "Time range: year/y (within 1 year), month/m (within 1 month), week/w (within 1 week), day/d (within 1 day)"
            ),
          chunksPerSource: z
            .number()
            .optional()
            .describe(
              "Number of chunks per source, e.g.: 3 for 3 chunks per source"
            ),
        })
        .optional()
        .describe("Search configuration options, all fields are optional"),
    },
    async handler(userId, { query, options = {} }) {
      try {
        const client = await getTavilyClient(userId);
        const response = await client.search(query, options);
        return { content: formatSearchResults(response) };
      } catch (error) {
        throw new Error(`Search failed: ${error.message}`);
      }
    },
  },
  {
    name: "tavily_extract",
    description: "Extract web page content from one or more specified URLs using Tavily Extract.",
    inputSchema: {
      urls: z
        .array(z.string())
        .describe(
          "List of URLs to extract content from (max 20). e.g.: ['https://example.com', 'https://test.com']"
        ),
      options: z
        .object({
          extractDepth: z
            .enum(["basic", "advanced"])
            .optional()
            .describe(
              "Extraction depth: basic (simple extraction) or advanced (detailed extraction)"
            ),
          includeImages: z
            .boolean()
            .optional()
            .describe("Include images in extraction: true or false"),
        })
        .optional()
        .describe(
          "Content extraction configuration options, all fields are optional"
        ),
    },
    async handler(userId, { urls, options = {} }) {
      try {
        const client = await getTavilyClient(userId);
        const response = await client.extract(urls, options);
        return { content: formatExtractResults(response) };
      } catch (error) {
        throw new Error(`Failed to extract content: ${error.message}`);
      }
    },
  },
];

// 工具函数：格式化 search 结果
function formatSearchResults(response) {
  const results = (response.results || []).map((result) => ({
    type: "text",
    text: `Title: ${result.title}\nContent: ${result.content}\nURL: ${result.url}\n${result.rawContent ? "RawContent: " + result.rawContent : ""}\n${result.publishedDate ? "PublishedDate: " + result.publishedDate : ""}`,
  }));
  const images = (response.images || []).map((result) => ({
    type: "text",
    text: `Image_URL: ${result.url}\n${result.description ? "Description: " + result.description : ""}`,
  }));
  return [...results, ...images];
}

// 工具函数：格式化 extract 结果
function formatExtractResults(response) {
  const content = (response.results || []).map((result) => ({
    type: "text",
    text: `URL: ${result.url}\nContent: ${result.rawContent}\n\n`,
  }));
  if (response.failedResults && response.failedResults.length > 0) {
    content.push({
      type: "text",
      text: `\nFailed to extract from URLs:\n${response.failedResults.join("\n")}`,
    });
  }
  return content;
}

export { tavilyTools };
