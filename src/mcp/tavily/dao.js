import mongoose from 'mongoose';

const tavilySchema = new mongoose.Schema({
  userId: { type: String, required: true, unique: true },
  apiKey: { type: String },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});

tavilySchema.index({ userId: 1 });

const Tavily = mongoose.model('Tavily', tavilySchema);

// Store Tavily API Key
const storeTavilyApiKey = async (userId, tavilyApiKey) => {
  await Tavily.findOneAndUpdate(
    { userId },
    { apiKey: tavilyApiKey, updatedAt: new Date() },
    { upsert: true, setDefaultsOnInsert: true }
  );
};

// Get Tavily API Key
const getTavilyApiKey = async (userId) => {
  const tavily = await Tavily.findOne({ userId });
  return tavily?.apiKey;
};

// Delete Tavily API Key
const deleteTavilyApiKey = async (userId) => {
  await Tavily.findOneAndUpdate(
    { userId },
    { $unset: { apiKey: 1 }, updatedAt: new Date() }
  );
};

export {
  storeTavilyApiKey,
  getTavilyApiKey,
  deleteTavilyApiKey
};
