import z from "zod";
import axios from "axios";

// 工具函数：获取高德地图API密钥
function getAmapApiKey() {
  const apiKey = '41822197fbf61e4eeeeb157cbc9f37ba';
  if (!apiKey) {
    throw new Error("AMAP_MAPS_API_KEY environment variable is not set");
  }
  return apiKey;
}

// 高德地图工具定义
const amapTools = [
  {
    name: "amap_regeocode",
    description: "将一个高德经纬度坐标转换为行政区划地址信息",
    inputSchema: {
      location: z.string().describe("经纬度坐标，格式为：经度,纬度"),
    },
    async handler(userId, { location }) {
      try {
        const apiKey = getAmapApiKey();
        const url = new URL("https://restapi.amap.com/v3/geocode/regeo");
        url.searchParams.append("location", location);
        url.searchParams.append("key", apiKey);
        url.searchParams.append("source", "ts_mcp");
        
        const response = await axios.get(url.toString());
        const data = response.data;
        
        if (data.status !== "1") {
          throw new Error(`RGeocoding failed: ${data.info || data.infocode}`);
        }
        
        const result = {
          province: data.regeocode.addressComponent.province,
          city: data.regeocode.addressComponent.city,
          district: data.regeocode.addressComponent.district
        };
        
        return { content: [{ type: "text", text: JSON.stringify(result, null, 2) }] };
      } catch (error) {
        throw new Error(`RGeocoding failed: ${error.message}`);
      }
    },
  },
  {
    name: "amap_geo",
    description: "将详细的结构化地址转换为经纬度坐标。支持对地标性名胜景区、建筑物名称解析为经纬度坐标",
    inputSchema: {
      address: z.string().describe("待解析的结构化地址信息"),
      city: z.string().optional().describe("指定查询的城市"),
    },
    async handler(userId, { address, city }) {
      try {
        const apiKey = getAmapApiKey();
        const url = new URL("https://restapi.amap.com/v3/geocode/geo");
        url.searchParams.append("key", apiKey);
        url.searchParams.append("address", address);
        if (city) {
          url.searchParams.append("city", city);
        }
        url.searchParams.append("source", "ts_mcp");
        
        const response = await axios.get(url.toString());
        const data = response.data;
        
        if (data.status !== "1") {
          throw new Error(`Geocoding failed: ${data.info || data.infocode}`);
        }
        
        const geocodes = data.geocodes || [];
        const result = geocodes.length > 0 ? geocodes.map((geo) => ({
          country: geo.country,
          province: geo.province,
          city: geo.city,
          citycode: geo.citycode,
          district: geo.district,
          street: geo.street,
          number: geo.number,
          adcode: geo.adcode,
          location: geo.location,
          level: geo.level
        })) : [];
        
        return { content: [{ type: "text", text: JSON.stringify({ return: result }, null, 2) }] };
      } catch (error) {
        throw new Error(`Geocoding failed: ${error.message}`);
      }
    },
  },
  {
    name: "amap_ip_location",
    description: "IP 定位根据用户输入的 IP 地址，定位 IP 的所在位置",
    inputSchema: {
      ip: z.string().describe("IP地址"),
    },
    async handler(userId, { ip }) {
      try {
        const apiKey = getAmapApiKey();
        const url = new URL("https://restapi.amap.com/v3/ip");
        url.searchParams.append("ip", ip);
        url.searchParams.append("key", apiKey);
        url.searchParams.append("source", "ts_mcp");
        
        const response = await axios.get(url.toString());
        const data = response.data;
        
        if (data.status !== "1") {
          throw new Error(`IP Location failed: ${data.info || data.infocode}`);
        }
        
        const result = {
          province: data.province,
          city: data.city,
          adcode: data.adcode,
          rectangle: data.rectangle
        };
        
        return { content: [{ type: "text", text: JSON.stringify(result, null, 2) }] };
      } catch (error) {
        throw new Error(`IP Location failed: ${error.message}`);
      }
    },
  },
  {
    name: "amap_weather",
    description: "根据城市名称或者标准adcode查询指定城市的天气",
    inputSchema: {
      city: z.string().describe("城市名称或者adcode"),
    },
    async handler(userId, { city }) {
      try {
        const apiKey = getAmapApiKey();
        const url = new URL("https://restapi.amap.com/v3/weather/weatherInfo");
        url.searchParams.append("city", city);
        url.searchParams.append("key", apiKey);
        url.searchParams.append("source", "ts_mcp");
        url.searchParams.append("extensions", "all");
        
        const response = await axios.get(url.toString());
        const data = response.data;
        
        if (data.status !== "1") {
          throw new Error(`Get weather failed: ${data.info || data.infocode}`);
        }
        
        const result = {
          city: data.forecasts[0].city,
          forecasts: data.forecasts[0].casts
        };
        
        return { content: [{ type: "text", text: JSON.stringify(result, null, 2) }] };
      } catch (error) {
        throw new Error(`Get weather failed: ${error.message}`);
      }
    },
  },
  {
    name: "amap_search_detail",
    description: "查询关键词搜或者周边搜获取到的POI ID的详细信息",
    inputSchema: {
      id: z.string().describe("关键词搜或者周边搜获取到的POI ID"),
    },
    async handler(userId, { id }) {
      try {
        const apiKey = getAmapApiKey();
        const url = new URL("https://restapi.amap.com/v3/place/detail");
        url.searchParams.append("id", id);
        url.searchParams.append("key", apiKey);
        url.searchParams.append("source", "ts_mcp");
        
        const response = await axios.get(url.toString());
        const data = response.data;
        
        if (data.status !== "1") {
          throw new Error(`Get poi detail failed: ${data.info || data.infocode}`);
        }
        
        const poi = data.pois[0];
        const result = {
          id: poi.id,
          name: poi.name,
          location: poi.location,
          address: poi.address,
          business_area: poi.business_area,
          city: poi.cityname,
          type: poi.type,
          alias: poi.alias,
          ...poi.biz_ext
        };
        
        return { content: [{ type: "text", text: JSON.stringify(result, null, 2) }] };
      } catch (error) {
        throw new Error(`Get poi detail failed: ${error.message}`);
      }
    },
  },
  {
    name: "amap_bicycling",
    description: "骑行路径规划用于规划骑行通勤方案，规划时会考虑天桥、单行线、封路等情况。最大支持 500km 的骑行路线规划",
    inputSchema: {
      origin: z.string().describe("出发点经纬度，坐标格式为：经度，纬度"),
      destination: z.string().describe("目的地经纬度，坐标格式为：经度，纬度"),
    },
    async handler(userId, { origin, destination }) {
      try {
        const apiKey = getAmapApiKey();
        const url = new URL("https://restapi.amap.com/v4/direction/bicycling");
        url.searchParams.append("key", apiKey);
        url.searchParams.append("origin", origin);
        url.searchParams.append("destination", destination);
        url.searchParams.append("source", "ts_mcp");
        
        const response = await axios.get(url.toString());
        const data = response.data;
        
        if (data.errcode !== 0) {
          throw new Error(`Direction bicycling failed: ${data.info || data.infocode}`);
        }
        
        const result = {
          data: {
            origin: data.data.origin,
            destination: data.data.destination,
            paths: data.data.paths.map((path) => ({
              distance: path.distance,
              duration: path.duration,
              steps: path.steps.map((step) => ({
                instruction: step.instruction,
                road: step.road,
                distance: step.distance,
                orientation: step.orientation,
                duration: step.duration,
              }))
            }))
          }
        };
        
        return { content: [{ type: "text", text: JSON.stringify(result, null, 2) }] };
      } catch (error) {
        throw new Error(`Direction bicycling failed: ${error.message}`);
      }
    },
  },
  {
    name: "amap_direction_walking",
    description: "步行路径规划 API 可以根据输入起点终点经纬度坐标规划100km 以内的步行通勤方案，并且返回通勤方案的数据",
    inputSchema: {
      origin: z.string().describe("出发点经度，纬度，坐标格式为：经度，纬度"),
      destination: z.string().describe("目的地经度，纬度，坐标格式为：经度，纬度"),
    },
    async handler(userId, { origin, destination }) {
      try {
        const apiKey = getAmapApiKey();
        const url = new URL("https://restapi.amap.com/v3/direction/walking");
        url.searchParams.append("key", apiKey);
        url.searchParams.append("origin", origin);
        url.searchParams.append("destination", destination);
        url.searchParams.append("source", "ts_mcp");
        
        const response = await axios.get(url.toString());
        const data = response.data;
        
        if (data.status !== "1") {
          throw new Error(`Direction Walking failed: ${data.info || data.infocode}`);
        }
        
        const result = {
          route: {
            origin: data.route.origin,
            destination: data.route.destination,
            paths: data.route.paths.map((path) => ({
              distance: path.distance,
              duration: path.duration,
              steps: path.steps.map((step) => ({
                instruction: step.instruction,
                road: step.road,
                distance: step.distance,
                orientation: step.orientation,
                duration: step.duration,
              }))
            }))
          }
        };
        
        return { content: [{ type: "text", text: JSON.stringify(result, null, 2) }] };
      } catch (error) {
        throw new Error(`Direction Walking failed: ${error.message}`);
      }
    },
  },
  {
    name: "amap_direction_driving",
    description: "驾车路径规划 API 可以根据用户起终点经纬度坐标规划以小客车、轿车通勤出行的方案，并且返回通勤方案的数据。",
    inputSchema: {
      origin: z.string().describe("出发点经度，纬度，坐标格式为：经度，纬度"),
      destination: z.string().describe("目的地经度，纬度，坐标格式为：经度，纬度"),
    },
    async handler(userId, { origin, destination }) {
      try {
        const apiKey = getAmapApiKey();
        const url = new URL("https://restapi.amap.com/v3/direction/driving");
        url.searchParams.append("key", apiKey);
        url.searchParams.append("origin", origin);
        url.searchParams.append("destination", destination);
        url.searchParams.append("source", "ts_mcp");
        
        const response = await axios.get(url.toString());
        const data = response.data;
        
        if (data.status !== "1") {
          throw new Error(`Direction Driving failed: ${data.info || data.infocode}`);
        }
        
        const result = {
          route: {
            origin: data.route.origin,
            destination: data.route.destination,
            paths: data.route.paths.map((path) => ({
              path: path.path,
              distance: path.distance,
              duration: path.duration,
              steps: path.steps.map((step) => ({
                instruction: step.instruction,
                road: step.road,
                distance: step.distance,
                orientation: step.orientation,
                duration: step.duration,
              }))
            }))
          }
        };
        
        return { content: [{ type: "text", text: JSON.stringify(result, null, 2) }] };
      } catch (error) {
        throw new Error(`Direction Driving failed: ${error.message}`);
      }
    },
  },
  {
    name: "amap_direction_transit_integrated",
    description: "公交路径规划 API 可以根据用户起终点经纬度坐标规划综合各类公共（火车、公交、地铁）交通方式的通勤方案，并且返回通勤方案的数据，跨城场景下必须传起点城市与终点城市",
    inputSchema: {
      origin: z.string().describe("出发点经度，纬度，坐标格式为：经度，纬度"),
      destination: z.string().describe("目的地经度，纬度，坐标格式为：经度，纬度"),
      city: z.string().describe("公共交通规划起点城市"),
      cityd: z.string().describe("公共交通规划终点城市"),
    },
    async handler(userId, { origin, destination, city, cityd }) {
      try {
        const apiKey = getAmapApiKey();
        const url = new URL("https://restapi.amap.com/v3/direction/transit/integrated");
        url.searchParams.append("key", apiKey);
        url.searchParams.append("origin", origin);
        url.searchParams.append("destination", destination);
        url.searchParams.append("city", city);
        url.searchParams.append("cityd", cityd);
        url.searchParams.append("source", "ts_mcp");
        
        const response = await axios.get(url.toString());
        const data = response.data;
        
        if (data.status !== "1") {
          throw new Error(`Direction Transit Integrated failed: ${data.info || data.infocode}`);
        }
        
        const result = {
          route: {
            origin: data.route.origin,
            destination: data.route.destination,
            distance: data.route.distance,
            transits: data.route.transits ? data.route.transits.map((transit) => ({
              duration: transit.duration,
              walking_distance: transit.walking_distance,
              segments: transit.segments ? transit.segments.map((segment) => ({
                walking: {
                  origin: segment.walking.origin,
                  destination: segment.walking.destination,
                  distance: segment.walking.distance,
                  duration: segment.walking.duration,
                  steps: segment.walking && segment.walking.steps ? segment.walking.steps.map((step) => ({
                    instruction: step.instruction,
                    road: step.road,
                    distance: step.distance,
                    action: step.action,
                    assistant_action: step.assistant_action
                  })) : [],
                },
                bus: {
                  buslines: segment.bus && segment.bus.buslines ? segment.bus.buslines.map((busline) => ({
                    name: busline.name,
                    departure_stop: {
                      name: busline.departure_stop.name
                    },
                    arrival_stop: {
                      name: busline.arrival_stop.name
                    },
                    distance: busline.distance,
                    duration: busline.duration,
                    via_stops: busline.via_stops ? busline.via_stops.map((via_stop) => ({
                      name: via_stop.name
                    })) : [],
                  })) : [],
                },
                entrance: {
                  name: segment.entrance.name
                },
                exit: {
                  name: segment.exit.name
                },
                railway: {
                  name: segment.railway.name,
                  trip: segment.railway.trip
                }
              })) : [],
            })) : [],
          }
        };
        
        return { content: [{ type: "text", text: JSON.stringify(result, null, 2) }] };
      } catch (error) {
        throw new Error(`Direction Transit Integrated failed: ${error.message}`);
      }
    },
  },
  {
    name: "amap_distance",
    description: "距离测量 API 可以测量两个经纬度坐标之间的距离,支持驾车、步行以及球面距离测量",
    inputSchema: {
      origins: z.string().describe("起点经度，纬度，可以传多个坐标，使用分号隔离，比如120,30;120,31，坐标格式为：经度，纬度"),
      destination: z.string().describe("终点经度，纬度，坐标格式为：经度，纬度"),
      type: z.string().optional().describe("距离测量类型,1代表驾车距离测量，0代表直线距离测量，3步行距离测量"),
    },
    async handler(userId, { origins, destination, type = "1" }) {
      try {
        const apiKey = getAmapApiKey();
        const url = new URL("https://restapi.amap.com/v3/distance");
        url.searchParams.append("key", apiKey);
        url.searchParams.append("origins", origins);
        url.searchParams.append("destination", destination);
        url.searchParams.append("type", type);
        url.searchParams.append("source", "ts_mcp");
        
        const response = await axios.get(url.toString());
        const data = response.data;
        
        if (data.status !== "1") {
          throw new Error(`Direction Distance failed: ${data.info || data.infocode}`);
        }
        
        const result = {
          results: data.results.map((result) => ({
            origin_id: result.origin_id,
            dest_id: result.dest_id,
            distance: result.distance,
            duration: result.duration
          }))
        };
        
        return { content: [{ type: "text", text: JSON.stringify(result, null, 2) }] };
      } catch (error) {
        throw new Error(`Direction Distance failed: ${error.message}`);
      }
    },
  },
  {
    name: "amap_text_search",
    description: "关键词搜，根据用户传入关键词，搜索出相关的POI",
    inputSchema: {
      keywords: z.string().describe("搜索关键词"),
      city: z.string().optional().describe("查询城市"),
      types: z.string().optional().describe("POI类型，比如加油站"),
    },
    async handler(userId, { keywords, city = "", types }) {
      try {
        const apiKey = getAmapApiKey();
        const url = new URL("https://restapi.amap.com/v3/place/text");
        url.searchParams.append("key", apiKey);
        url.searchParams.append("keywords", keywords);
        url.searchParams.append("city", city);
        if (types) {
          url.searchParams.append("types", types);
        }
        url.searchParams.append("source", "ts_mcp");
        
        const response = await axios.get(url.toString());
        const data = response.data;
        
        if (data.status !== "1") {
          throw new Error(`Text Search failed: ${data.info || data.infocode}`);
        }
        
        const resciytes = data.suggestion && data.suggestion.ciytes ? data.suggestion.ciytes.map((city) => ({
          name: city.name
        })) : [];
        
        const result = {
          suggestion: {
            keywords: data.suggestion.keywords,
            ciytes: resciytes,
          },
          pois: data.pois.map((poi) => ({
            id: poi.id,
            name: poi.name,
            address: poi.address,
            typecode: poi.typecode
          }))
        };
        
        return { content: [{ type: "text", text: JSON.stringify(result, null, 2) }] };
      } catch (error) {
        throw new Error(`Text Search failed: ${error.message}`);
      }
    },
  },
  {
    name: "amap_around_search",
    description: "周边搜，根据用户传入关键词以及坐标location，搜索出radius半径范围的POI",
    inputSchema: {
      location: z.string().describe("中心点经度纬度"),
      radius: z.string().optional().describe("搜索半径"),
      keywords: z.string().optional().describe("搜索关键词"),
    },
    async handler(userId, { location, radius = "1000", keywords = "" }) {
      try {
        const apiKey = getAmapApiKey();
        const url = new URL("https://restapi.amap.com/v3/place/around");
        url.searchParams.append("key", apiKey);
        url.searchParams.append("location", location);
        url.searchParams.append("radius", radius);
        url.searchParams.append("keywords", keywords);
        url.searchParams.append("source", "ts_mcp");
        
        const response = await axios.get(url.toString());
        const data = response.data;
        
        if (data.status !== "1") {
          throw new Error(`Around Search failed: ${data.info || data.infocode}`);
        }
        
        const result = {
          pois: data.pois.map((poi) => ({
            id: poi.id,
            name: poi.name,
            address: poi.address,
            typecode: poi.typecode
          }))
        };
        
        return { content: [{ type: "text", text: JSON.stringify(result, null, 2) }] };
      } catch (error) {
        throw new Error(`Around Search failed: ${error.message}`);
      }
    },
  },
];

export { amapTools }; 