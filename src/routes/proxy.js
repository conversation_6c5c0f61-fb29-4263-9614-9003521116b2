import express from "express";
import axios from "axios";
import { getUserIdByApiKey } from "../dao/user.js";

const router = express.Router();

// 代理请求处理中间件
async function proxyHandler(req, res) {
  const apiKey = req.query.api_key || req.query.API_KEY;

  if (!apiKey) {
    res.status(401).json({ error: "API key is required" });
    return;
  }

  try {
    // 检查API key是否存在于Redis中
    const userId = await getUserIdByApiKey(apiKey);

    if (!userId) {
      res.status(401).json({ error: "Invalid API key" });
      return;
    }

    // 从环境变量获取后端URL
    const targetUrl = process.env.BACKEND_URL;
    if (!targetUrl) {
      throw new Error("BACKEND_URL environment variable is not set");
    }

    // 构建请求配置
    const config = {
      method: req.method,
      url: `${targetUrl}${req.path}`,
      headers: {
        ...req.headers,
        host: new URL(targetUrl).host,
      },
      params: req.query,
      data: req.body,
    };

    // 发送请求到后端服务
    const response = await axios(config);

    // 设置响应
    res.status(response.status);
    res.set(response.headers);
    res.json(response.data);
  } catch (error) {
    console.error("Proxy error:", error);
    if (error.message === "BACKEND_URL environment variable is not set") {
      res.status(500).json({ error: "Backend service configuration error" });
    } else {
      res
        .status(error.response?.status || 500)
        .json({ error: "Internal server error" });
    }
  }
}

// 注册代理路由
router.all("/model/*splat", proxyHandler);

export default router;
