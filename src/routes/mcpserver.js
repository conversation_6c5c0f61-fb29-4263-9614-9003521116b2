import express from "express";
import { randomUUID } from "node:crypto";
import { StreamableHTTPServerTransport } from "@modelcontextprotocol/sdk/server/streamableHttp.js";
import { isInitializeRequest } from "@modelcontextprotocol/sdk/types.js";
import { createServerForDirectory, getAvailableMCPDirectories } from "../services/mcpserver.js";
import { getUserIdByApiKey } from "../dao/user.js";
import { createConnection } from "@playwright/mcp";
// Note: @antv/mcp-server-chart doesn't export createServer properly, so we'll use createRequire with absolute path
import { createRequire } from "module";
import path from "path";
import { fileURLToPath } from "url";
import multer from "multer";
import { layoutParsing } from "../services/file-cool/paddle/client.js";
import { processMdToDocx } from "../services/file-cool/gongwen/index.js";
import { excelToMarkdown } from "../services/file-cool/excel/index.js";
import { processFileWithZhipu } from "../services/file-cool/zhipu/index.js";

// Create require instance to import chart server using absolute path
const require = createRequire(import.meta.url);
const __dirname = path.dirname(fileURLToPath(import.meta.url));
const chartServerPath = path.join(__dirname, "../../node_modules/@antv/mcp-server-chart/build/server.js");
const { createServer: createAntDesignServer } = require(chartServerPath);

const router = express.Router();

// Configure multer for handling multipart form data
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 100 * 1024 * 1024, // 100MB limit
  },
});

const requireAuth = async (req, res, next) => {
  const apiKey = req.headers["api_key"] || req.headers["API_KEY"];
  if (!apiKey) {
    res.status(401).json({ error: "API key is required" });
    return;
  }
  const userId = await getUserIdByApiKey(apiKey);
  if (!userId) {
    res.status(401).json({ error: "Invalid API key" });
    return;
  }
  req.userId = userId;
  next();
};

// Map to store playwright transports by session ID
const playwrightTransports = {};

// Map to store chart transports by session ID
const chartTransports = {};

// Map to store MCP directory transports by session ID
const mcpDirectoryTransports = {};

// Playwright MCP server routes
// Handle POST requests for playwright client-to-server communication
router.post("/playwright", requireAuth, async (req, res) => {
  const sessionId = req.headers["mcp-session-id"];
  let transport;

  if (sessionId && playwrightTransports[sessionId]) {
    // Reuse existing transport
    transport = playwrightTransports[sessionId];
  } else if (!sessionId && isInitializeRequest(req.body)) {
    // New initialization request for playwright
    transport = new StreamableHTTPServerTransport({
      sessionIdGenerator: () => randomUUID(),
      onsessioninitialized: (sessionId) => {
        // Store the playwright transport by session ID
        playwrightTransports[sessionId] = transport;
      },
    });

    // Clean up transport when closed
    transport.onclose = () => {
      if (transport.sessionId) {
        delete playwrightTransports[transport.sessionId];
      }
    };

    // Create playwright MCP server connection
    const playwrightServer = await createConnection({
      browser: { launchOptions: { headless: true } },
    });
    await playwrightServer.server.connect(transport);
  } else {
    // Invalid request
    res.status(400).json({
      jsonrpc: "2.0",
      error: {
        code: -32000,
        message: "Bad Request: No valid session ID provided for playwright",
      },
      id: null,
    });
    return;
  }

  // Handle the request
  await transport.handleRequest(req, res, req.body);
});

// Reusable handler for playwright GET and DELETE requests
const handlePlaywrightSessionRequest = async (req, res) => {
  const sessionId = req.headers["mcp-session-id"];
  if (!sessionId || !playwrightTransports[sessionId]) {
    res.status(400).send("Invalid or missing session ID for playwright");
    return;
  }

  const transport = playwrightTransports[sessionId];
  await transport.handleRequest(req, res);
};

// Handle GET requests for playwright server-to-client notifications via SSE
router.get("/playwright", requireAuth, handlePlaywrightSessionRequest);

// Handle DELETE requests for playwright session termination
router.delete("/playwright", requireAuth, handlePlaywrightSessionRequest);

// Chart MCP server routes
// Handle POST requests for chart client-to-server communication
router.post("/chart", requireAuth, async (req, res) => {
  const sessionId = req.headers["mcp-session-id"];
  let transport;

  if (sessionId && chartTransports[sessionId]) {
    // Reuse existing transport
    transport = chartTransports[sessionId];
  } else if (!sessionId && isInitializeRequest(req.body)) {
    // New initialization request for chart
    transport = new StreamableHTTPServerTransport({
      sessionIdGenerator: () => randomUUID(),
      onsessioninitialized: (sessionId) => {
        // Store the chart transport by session ID
        chartTransports[sessionId] = transport;
      },
    });

    // Clean up transport when closed
    transport.onclose = () => {
      if (transport.sessionId) {
        delete chartTransports[transport.sessionId];
      }
    };

    // Create chart MCP server connection
    const chartServer = await createAntDesignServer();
    await chartServer.connect(transport);
  } else {
    // Invalid request
    res.status(400).json({
      jsonrpc: "2.0",
      error: {
        code: -32000,
        message: "Bad Request: No valid session ID provided for chart",
      },
      id: null,
    });
    return;
  }

  // Handle the request
  await transport.handleRequest(req, res, req.body);
});

// Reusable handler for chart GET and DELETE requests
const handleChartSessionRequest = async (req, res) => {
  const sessionId = req.headers["mcp-session-id"];
  if (!sessionId || !chartTransports[sessionId]) {
    res.status(400).send("Invalid or missing session ID for chart");
    return;
  }

  const transport = chartTransports[sessionId];
  await transport.handleRequest(req, res);
};

// Handle GET requests for chart server-to-client notifications via SSE
router.get("/chart", requireAuth, handleChartSessionRequest);

// Handle DELETE requests for chart session termination
router.delete("/chart", requireAuth, handleChartSessionRequest);

router.post(
  "/file-cool",
  requireAuth,
  upload.array("inputFiles"),
  async (req, res) => {
    try {
      const { functionType } = req.body;
      const files = req.files;

      if (!functionType) {
        return res.status(400).json({ error: "functionType is required" });
      }

      if (!files || files.length === 0) {
        return res.status(400).json({ error: "No files uploaded" });
      }

      if (functionType === "paddle_ocr") {
        const results = [];

        for (const file of files) {
          // Call the paddle layout parsing service
          const fileType = file.originalname.toLowerCase().endsWith(".pdf")
            ? 0
            : 1;
          const result = await layoutParsing(
            file.buffer,
            file.originalname,
            fileType
          );
          results.push({
            filename: file.originalname,
            result: result,
          });
        }

        return res.json(results);
      } else if (functionType === "md2gongwen") {
        const results = [];
        for (const file of files) {
          const result = await processMdToDocx(file.buffer, file.originalname);
          results.push({
            filename: file.originalname,
            result: result,
          });
        }
        return res.json(results);
      } else if (functionType === "excel2md") {
        const results = [];
        for (const file of files) {
          const result = await excelToMarkdown(file.buffer, file.originalname);
          results.push({
            filename: file.originalname,
            result: result,
          });
        }
        return res.json(results);
      } else if (functionType === "image2md") {
        const results = [];
        for (const file of files) {
          const result = await processFileWithZhipu(file.buffer, file.originalname);
          results.push({
            filename: file.originalname,
            result: result,
          });
        }
        return res.json(results);
      } else {
        return res.status(400).json({ error: "Unsupported functionType" });
      }
    } catch (error) {
      console.error("Error processing file-cool request:", error);
      return res.status(500).json({ error: "Internal server error" });
    }
  }
);

// GET /file-cool/tools - Returns the list of file-cool tools with name and description
router.get("/file-cool/tools", requireAuth, (req, res) => {
  // Currently, only one tool is defined in file-cool/src/index.ts
  const tools = [
    {
      name: "paddle_ocr",
      description:
        "Execute a OCR from pdf or image file convert to markdown using PaddleOCR",
    },
    {
      name: "md2gongwen",
      description: "Execute a markdown convert to Gongwen format docx",
    },
    {
      name: "excel2md",
      description: "Execute a excel convert to markdown",
    },
    {
      name: "image2md",
      description: "Execute image analysis and convert to markdown using Zhipu AI GLM-4.1V-Thinking-Flash model",
    },
  ];
  res.json(tools);
});

// GET /server-list - Returns the list of streamable mcp server with name and url
router.get("/server-list", requireAuth, (req, res) => {
  const availableDirectories = getAvailableMCPDirectories();
  
  const list = [
    {
      name: "playwright",
      path: "playwright",
    },
    {
      name: "chart",
      path: "chart",
    },
    // Add dynamic MCP directory servers
    ...availableDirectories.map(dirName => ({
      name: `${dirName}`,
      path: `${dirName}`,
    }))
  ];
  res.json(list);
});

// Dynamic route generation for MCP directories
function createMCPDirectoryRoutes() {
  const availableDirectories = getAvailableMCPDirectories();
  
  availableDirectories.forEach(dirName => {
    const routePath = `/${dirName}`;
    
    // Handle POST requests for MCP directory client-to-server communication
    router.post(routePath, requireAuth, async (req, res) => {
      const sessionId = req.headers["mcp-session-id"];
      let transport;

      if (sessionId && mcpDirectoryTransports[sessionId]) {
        // Reuse existing transport
        transport = mcpDirectoryTransports[sessionId];
      } else if (!sessionId && isInitializeRequest(req.body)) {
        // New initialization request for MCP directory
        transport = new StreamableHTTPServerTransport({
          sessionIdGenerator: () => randomUUID(),
          onsessioninitialized: (sessionId) => {
            // Store the MCP directory transport by session ID
            mcpDirectoryTransports[sessionId] = transport;
          },
        });

        // Clean up transport when closed
        transport.onclose = () => {
          if (transport.sessionId) {
            delete mcpDirectoryTransports[transport.sessionId];
          }
        };

        // Create MCP directory server connection
        const mcpServer = await createServerForDirectory(req.userId, dirName);
        await mcpServer.connect(transport);
      } else {
        // Invalid request
        res.status(400).json({
          jsonrpc: "2.0",
          error: {
            code: -32000,
            message: `Bad Request: No valid session ID provided for ${dirName}`,
          },
          id: null,
        });
        return;
      }

      // Handle the request
      await transport.handleRequest(req, res, req.body);
    });

    // Reusable handler for MCP directory GET and DELETE requests
    const handleMCPDirectorySessionRequest = async (req, res) => {
      const sessionId = req.headers["mcp-session-id"];
      if (!sessionId || !mcpDirectoryTransports[sessionId]) {
        res.status(400).send(`Invalid or missing session ID for ${dirName}`);
        return;
      }

      const transport = mcpDirectoryTransports[sessionId];
      await transport.handleRequest(req, res);
    };

    // Handle GET requests for MCP directory server-to-client notifications via SSE
    router.get(routePath, requireAuth, handleMCPDirectorySessionRequest);

    // Handle DELETE requests for MCP directory session termination
    router.delete(routePath, requireAuth, handleMCPDirectorySessionRequest);
  });
}

// Initialize dynamic routes
createMCPDirectoryRoutes();

export default router;
