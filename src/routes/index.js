import express from "express";
import { v4 as uuidv4 } from "uuid";
import { store<PERSON><PERSON><PERSON><PERSON>, get<PERSON><PERSON><PERSON><PERSON>, delete<PERSON><PERSON><PERSON><PERSON> } from "../dao/user.js";
import axios from "axios";
import fs from 'fs';
import path from 'path';
import { requireAuth } from "../middlewares/auth.js";

const router = express.Router();

// CAS SSO configuration
const casConfig = {
  cas_url: process.env.CAS_URL || '',
  service_url: process.env.SERVICE_URL || '',
  appId: process.env.APP_ID || '',
};

/**
 * Route handlers
 */

// Login route
router.get("/login", (req, res) => {
  if (req.session.user) {
    return res.redirect("/dashboard");
  }
  res.render("login");
});

// CAS login route
router.get("/cas/login", (req, res) => {
  const loginUrl = `${casConfig.cas_url}sso/login?appId=${casConfig.appId}&redirect=${encodeURIComponent(casConfig.service_url)}`;
  res.redirect(loginUrl);
});

// CAS callback route
router.get("/cas/callback", async (req, res) => {
  try {
    let ticket = req.query.ticket;
    if (!ticket) {
      return res.redirect("/login");
    }
    
    try {
      ticket = JSON.parse(ticket).ticket;
      if (!ticket) {
        return res.redirect("/login");
      }
    } catch (parseError) {
      console.error("Invalid ticket format:", parseError);
      return res.redirect("/login");
    }

    // Validate the ticket with CAS server
    const validateUrl = `${casConfig.cas_url}/api/api/sso/user?ticket=${ticket}`;
    const response = await axios.get(validateUrl);

    // Handle JSON response
    const { code, data } = response.data;

    if (code === 0 && data?.user) {
      req.session.user = {
        id: data.user.username,
        username: data.user.username,
      };

      return res.redirect("/dashboard");
    } else {
      console.error("CAS authentication failed:", response.data);
      return res.redirect("/login");
    }
  } catch (error) {
    console.error("CAS validation error:", error);
    return res.redirect("/login");
  }
});

// Dashboard route
router.get("/dashboard", requireAuth, async (req, res) => {
  try {
    const apiKey = await getApiKey(req.session.user.id);
    const partialsData = await loadPartials(req.session.user.id);
    
    res.render("dashboard", {
      user: req.session.user,
      apiKey,
      partialsList: partialsData.partialsList,
      ...partialsData.data
    });
  } catch (error) {
    console.error("Dashboard rendering error:", error);
    res.status(500).send("An error occurred while loading the dashboard");
  }
});

// Generate API Key route
router.post("/api/generate-key", requireAuth, async (req, res) => {
  try {
    const apiKey = uuidv4();
    await storeApiKey(req.session.user.id, apiKey);
    res.json({ apiKey });
  } catch (error) {
    console.error("API key generation error:", error);
    res.status(500).json({ error: "Failed to generate API key" });
  }
});

// Delete API Key route
router.delete("/api/delete-key", requireAuth, async (req, res) => {
  try {
    await deleteApiKey(req.session.user.id);
    res.status(204).send();
  } catch (error) {
    console.error("API key deletion error:", error);
    res.status(500).json({ error: "Failed to delete API key" });
  }
});

// Logout route
router.get("/logout", (req, res) => {
  if (req.session.user) {
    const logoutUrl = `${casConfig.cas_url}sso/logout?redirect=${encodeURIComponent(casConfig.service_url)}`;
    req.session.destroy(err => {
      if (err) console.error("Session destruction error:", err);
      res.redirect(logoutUrl);
    });
  } else {
    res.redirect("/login");
  }
});

/**
 * Helper functions
 */

// Load partials and their data
async function loadPartials(userId) {
  const partialsDir = path.join(process.cwd(), 'src', 'mcp');
  let partialsList = [];
  let data = {};
  
  try {
    const dirs = fs.readdirSync(partialsDir)
      .filter(file => fs.statSync(path.join(partialsDir, file)).isDirectory());
    
    for (const dir of dirs) {
      const partialPath = path.join(partialsDir, dir, 'partials.ejs');
      if (fs.existsSync(partialPath)) {
        partialsList.push(partialPath);
      }
      
      const partialsDataPath = path.join(partialsDir, dir, 'partialsData.js');
      if (fs.existsSync(partialsDataPath)) {
        try {
          const mod = await import(path.resolve(partialsDataPath));
          if (mod.getData && typeof mod.getData === 'function') {
            data[dir] = await mod.getData(userId);
          }
        } catch (err) {
          console.error(`Failed to load ${dir}/partialsData.js:`, err);
        }
      }
    }
    
    return { partialsList, data };
  } catch (error) {
    console.error('Failed to read partials directory:', error);
    return { partialsList: [], data: {} };
  }
}

export default router;

