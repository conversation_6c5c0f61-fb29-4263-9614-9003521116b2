import mongoose from "mongoose";

const userSchema = new mongoose.Schema({
  userId: { type: String, required: true, unique: true },
  apiKey: { type: String, unique: true, sparse: true },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now },
});
userSchema.index({ apiKey: 1 });
userSchema.index({ userId: 1 });
const User = mongoose.model("User", userSchema);

// API Key operations
const storeApiKey = async (userId, apiKey) => {
  await User.findOneAndUpdate(
    { userId },
    { apiKey, updatedAt: new Date() },
    { upsert: true }
  );
};

const getApiKey = async (userId) => {
  const user = await User.findOne({ userId });
  return user?.apiKey;
};

const deleteApiKey = async (userId) => {
  await User.findOneAndUpdate(
    { userId },
    { $unset: { apiKey: 1 }, updatedAt: new Date() }
  );
};

const getUserIdByApiKey = async (apiKey) => {
  const user = await User.findOne({ apiKey });
  return user?.userId;
};

export {
  User,
  storeApiKey,
  getApiKey,
  deleteApiKey,
  getUserIdByApiKey,
};
