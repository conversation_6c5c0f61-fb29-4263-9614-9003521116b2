# MCP Gateway

A gateway server with CAS SSO authentication and API Key management.

## Features

- CAS SSO Authentication
- API Key generation and management
- Redis-based API Key storage
- Modern UI with Tailwind CSS

## Prerequisites

- Node.js (v14 or higher)
- Redis server
- CAS SSO server

## Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd mcp-gateway
```

2. Install dependencies:
```bash
npm install
```

3. Create environment file:
```bash
cp .env.example .env
```

4. Update the `.env` file with your configuration:
- Set your CAS server URL
- Configure Redis connection
- Set a secure session secret

## Running the Application

Development mode:
```bash
npm run dev
```

Production mode:
```bash
npm start
```

## API Endpoints

- `GET /login` - Login page
- `GET /cas/callback` - CAS SSO callback
- `GET /dashboard` - User dashboard
- `POST /api/generate-key` - Generate new API key
- `DELETE /api/delete-key` - Delete existing API key
- `GET /logout` - Logout

## Security Considerations

- API Keys are stored in Redis with a 1-year expiration
- Session cookies are HTTP-only and secure
- All sensitive routes require authentication
- CAS SSO integration for secure authentication

## License

MIT 